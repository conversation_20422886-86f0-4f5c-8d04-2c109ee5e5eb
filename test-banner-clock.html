<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅时钟测试</title>
    <link rel="stylesheet" href="http://localhost:4000/css/custom.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        #banners {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>横幅时钟组件测试</h1>
        <p>这个页面用于测试左侧横幅中的时钟组件是否正常工作。</p>
        <p>时钟应该显示在横幅的左下角，包含：</p>
        <ul>
            <li>当前时间（时:分）</li>
            <li>当前日期（月日 星期）</li>
            <li>位置信息（北京, 中国）</li>
        </ul>
    </div>
    
    <div id="banners">
        <!-- 横幅时钟将通过JavaScript动态添加到这里 -->
    </div>
    
    <script>
        // 创建横幅时钟
        function createBannerClock() {
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time"></div>
                    <div class="banner-clock-date"></div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>北京, 中国</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
                
                // 更新横幅时钟
                function updateBannerClock() {
                    const timeEl = bannerClock.querySelector('.banner-clock-time');
                    const dateEl = bannerClock.querySelector('.banner-clock-date');
                    if (timeEl && dateEl) {
                        const now = new Date();
                        timeEl.textContent = now.toLocaleTimeString('zh-CN', {
                            hour12: false,
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        dateEl.textContent = now.toLocaleDateString('zh-CN', {
                            month: 'short',
                            day: 'numeric',
                            weekday: 'short'
                        });
                    }
                }
                updateBannerClock();
                setInterval(updateBannerClock, 1000);
            }
        }
        
        // 页面加载完成后创建时钟
        document.addEventListener('DOMContentLoaded', createBannerClock);
    </script>
</body>
</html>
