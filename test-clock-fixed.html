<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅时钟修复测试</title>
    <link rel="stylesheet" href="public/css/custom.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        #banners {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🕐 横幅时钟修复测试</h1>
        <p>这个页面用于验证横幅时钟组件的修复是否成功。</p>
        <p><strong>修复内容：</strong></p>
        <ul>
            <li>✅ 修复时间格式显示问题（确保显示为 HH:MM:SS 格式）</li>
            <li>✅ 修复日期格式显示问题（确保显示为中文格式）</li>
            <li>✅ 修复文件路径引用问题</li>
            <li>✅ 优化备用时钟逻辑</li>
        </ul>
    </div>
    
    <div id="banners">
        <!-- 横幅时钟将通过JavaScript动态添加到这里 -->
    </div>
    
    <div class="status">
        <h3>✅ 修复状态</h3>
        <p>如果您看到左下角的时钟正确显示时间（格式：HH:MM:SS）和日期（格式：M月D日星期X），说明修复成功！</p>
        <p>时钟应该每秒更新一次，显示准确的当前时间。</p>
    </div>
    
    <script src="public/js/banner-clock.js"></script>
    <script>
        // 创建横幅时钟
        function createBannerClock() {
            console.log('🚀 开始创建横幅时钟...');
            
            const banners = document.getElementById('banners');
            if (!banners) {
                console.error('❌ 找不到 banners 元素');
                return;
            }
            
            if (document.getElementById('banner-clock')) {
                console.log('⚠️ banner-clock 元素已存在');
                return;
            }
            
            const bannerClock = document.createElement('div');
            bannerClock.id = 'banner-clock';
            bannerClock.innerHTML = `
                <div class="banner-clock-time">加载中...</div>
                <div class="banner-clock-date">加载中...</div>
                <div class="banner-clock-weather">
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-cloud-sun"></i>
                        <span>多云 25°C</span>
                    </div>
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-tint"></i>
                        <span>65%</span>
                    </div>
                </div>
                <div class="banner-clock-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>获取位置中...</span>
                </div>
            `;
            banners.appendChild(bannerClock);
            console.log('✅ 横幅时钟 DOM 元素已创建');
            
            // 检查 BannerClockWidget 是否可用
            setTimeout(() => {
                if (typeof BannerClockWidget !== 'undefined') {
                    console.log('✅ BannerClockWidget 类已加载，开始初始化...');
                    try {
                        new BannerClockWidget();
                        console.log('✅ BannerClockWidget 初始化成功');
                    } catch (error) {
                        console.error('❌ BannerClockWidget 初始化失败:', error);
                        startBackupClock();
                    }
                } else {
                    console.warn('❌ BannerClockWidget 类未定义，使用备用时钟');
                    startBackupClock();
                }
            }, 100);
            
            // 备用简单时钟
            function startBackupClock() {
                function updateBannerClock() {
                    const timeEl = bannerClock.querySelector('.banner-clock-time');
                    const dateEl = bannerClock.querySelector('.banner-clock-date');
                    if (timeEl && dateEl) {
                        const now = new Date();
                        // 手动格式化时间以确保正确的格式
                        const hours = String(now.getHours()).padStart(2, '0');
                        const minutes = String(now.getMinutes()).padStart(2, '0');
                        const seconds = String(now.getSeconds()).padStart(2, '0');
                        timeEl.textContent = `${hours}:${minutes}:${seconds}`;
                        
                        // 格式化日期
                        const year = now.getFullYear();
                        const month = now.getMonth() + 1;
                        const day = now.getDate();
                        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                        const weekday = weekdays[now.getDay()];
                        dateEl.textContent = `${month}月${day}日${weekday}`;
                    }
                }
                updateBannerClock();
                setInterval(updateBannerClock, 1000);
                console.log('✅ 备用时钟已启动');
            }
        }
        
        // 页面加载完成后创建时钟
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面 DOM 加载完成');
            createBannerClock();
        });
    </script>
</body>
</html>
