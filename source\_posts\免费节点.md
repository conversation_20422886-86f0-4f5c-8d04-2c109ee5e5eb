---
title: 免费内容
date: 2025-06-27 01:28:39
categories: 
- 网络工具
tags: 
- 代理
- 节点
cover: https://cdn4.winhlb.com/2025/06/27/685d830497f1e.jpeg
---

<style>
.terminal-block {
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 6px;
    margin: 10px 0;
    position: relative;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.terminal-header {
    background: #2d2d2d;
    padding: 8px 12px;
    border-bottom: 1px solid #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 6px 6px 0 0;
}

.terminal-title {
    color: #888;
    font-size: 12px;
    font-weight: normal;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f56; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #27ca3f; }

.terminal-content {
    padding: 12px;
    color: #f8f8f2;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
}

.terminal-prompt {
    color: #50fa7b;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #44475a;
    border: 1px solid #6272a4;
    color: #f8f8f2;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
}

.copy-btn:hover {
    background: #6272a4;
    border-color: #8be9fd;
}

.copy-btn.success {
    background: #50fa7b;
    color: #282a36;
    border-color: #50fa7b;
}

.copy-btn.error {
    background: #ff5555;
    border-color: #ff5555;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.innerHTML;
        button.innerHTML = '✓ 已复制';
        button.className = 'copy-btn success';
        setTimeout(function() {
            button.innerHTML = originalText;
            button.className = 'copy-btn';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
        const originalText = button.innerHTML;
        button.innerHTML = '✗ 失败';
        button.className = 'copy-btn error';
        setTimeout(function() {
            button.innerHTML = originalText;
            button.className = 'copy-btn';
        }, 2000);
    });
}
</script>

## 免责声明
本文档仅用于学习和教育目的，旨在帮助安全研究人员和开发者了解网络技术。本博文中所包含的信息和工具仅用于合法的安全测试和研究，不得用于任何非法活动。

使用本文所提供的信息进行任何未经授权的行为均为非法行为，违反法律将导致严重的法律后果。读者在使用这些信息时，必须确保拥有合法的授权，并严格遵守所在国家和地区的法律法规。

作者不对任何因使用本文内容而导致的直接或间接损害承担责任。所有风险和责任由用户自行承担。

此外，读者必须在24小时内删除产生的内容，以确保信息不会被滥用。

## 汇聚订阅

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">汇聚订阅地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://sub.0407123.xyz/admin?sub
        <button class="copy-btn" onclick="copyTerminalContent('https://sub.0407123.xyz/admin?sub', this)">复制</button>
    </div>
</div>

## 节点列表

### 1. webhostmost 代理节点（三网优化）



**主要节点：**

**美国节点：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">美国节点</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://usa1.0407123.xyz/5a804300-404e-4bbd-b5f0-9e99c417cd69
        <button class="copy-btn" onclick="copyTerminalContent('https://usa1.0407123.xyz/5a804300-404e-4bbd-b5f0-9e99c417cd69', this)">复制</button>
    </div>
</div>

**印度节点：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">印度节点</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://in1.0407123.xyz/0e16c475-f72e-4f9e-97a1-6cfce52f34f8
        <button class="copy-btn" onclick="copyTerminalContent('https://in1.0407123.xyz/0e16c475-f72e-4f9e-97a1-6cfce52f34f8', this)">复制</button>
    </div>
</div>

**荷兰备用：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">荷兰备用</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://hl.0407123.xyz/67d1d86b-0f2d-4db9-8b8c-d886ff2f43ee
        <button class="copy-btn" onclick="copyTerminalContent('https://hl.0407123.xyz/67d1d86b-0f2d-4db9-8b8c-d886ff2f43ee', this)">复制</button>
    </div>
</div>

**备用节点：**
> 当上述节点失效时使用

**管理面板：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">管理面板</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://dl.qianxiu.ddns-ip.net/panel
        <button class="copy-btn" onclick="copyTerminalContent('https://dl.qianxiu.ddns-ip.net/panel', this)">复制</button>
    </div>
</div>

**登录密码：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">登录密码</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>Ch123456789...,,
        <button class="copy-btn" onclick="copyTerminalContent('Ch123456789...,,', this)">复制</button>
    </div>
</div>

### 2. EdgeTunnel 节点

**自建地址：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">自建地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://sub.qianxiu.ddns-ip.net/9b89ed4a-6731-4d31-a143-27fe3817b981?sub
        <button class="copy-btn" onclick="copyTerminalContent('https://sub.qianxiu.ddns-ip.net/9b89ed4a-6731-4d31-a143-27fe3817b981?sub', this)">复制</button>
    </div>
</div>

**使用方法：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">使用方法模板</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://sub.qianxiu.ddns-ip.net/9b89ed4a-6731-4d31-a143-27fe3817b981?sub=订阅器地址
        <button class="copy-btn" onclick="copyTerminalContent('https://sub.qianxiu.ddns-ip.net/9b89ed4a-6731-4d31-a143-27fe3817b981?sub=订阅器地址', this)">复制</button>
    </div>
</div>

**示例：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">使用示例</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://sub.qianxiu.ddns-ip.net/9b89ed4a-6731-4d31-a143-27fe3817b981?sub=sub.cmliussss.net
        <button class="copy-btn" onclick="copyTerminalContent('https://sub.qianxiu.ddns-ip.net/9b89ed4a-6731-4d31-a143-27fe3817b981?sub=sub.cmliussss.net', this)">复制</button>
    </div>
</div>

## 订阅器地址

### 可用订阅器列表

#### CM 订阅器
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">CM 订阅器</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>sub.cmliussss.net
        <button class="copy-btn" onclick="copyTerminalContent('sub.cmliussss.net', this)">复制</button>
    </div>
</div>
来源：[Telegram](https://t.me/CMLiussss_channel)

#### Moist_R 订阅器
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">Moist_R 订阅器</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>owo.o00o.ooo
        <button class="copy-btn" onclick="copyTerminalContent('owo.o00o.ooo', this)">复制</button>
    </div>
</div>

解锁GPT:
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">解锁GPT</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>sjc.o00o.ooo
        <button class="copy-btn" onclick="copyTerminalContent('sjc.o00o.ooo', this)">复制</button>
    </div>
</div>
来源：[Telegram](@mianfeicf)

#### 周润发 订阅器
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">周润发 订阅器</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>zrf.zrf.me
        <button class="copy-btn" onclick="copyTerminalContent('zrf.zrf.me', this)">复制</button>
    </div>
</div>
来源：[Telegram](https://t.me/lsmkc/479)

#### 文烨 订阅器
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">文烨 订阅器</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>sub.keaeye.icu
        <button class="copy-btn" onclick="copyTerminalContent('sub.keaeye.icu', this)">复制</button>
    </div>
</div>
来源：[Telegram](https://t.me/keaeyequn/175)

#### Kristi 订阅器
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">Kristi 订阅器</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>sub.mot.cloudns.biz
        <button class="copy-btn" onclick="copyTerminalContent('sub.mot.cloudns.biz', this)">复制</button>
    </div>
</div>
来源：[Telegram](https://t.me/Marisa_kristi/30)

#### 青云志 订阅器
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">青云志 订阅器</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>dy.yomoh.ggff.net
        <button class="copy-btn" onclick="copyTerminalContent('dy.yomoh.ggff.net', this)">复制</button>
    </div>
</div>
来源：[官网](https://dy.yomoh.ggff.net/)

### 使用说明

将上述订阅地址替换到 EdgeTunnel 节点的 `sub` 参数中即可使用。
