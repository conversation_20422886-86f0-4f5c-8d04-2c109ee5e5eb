<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层级位置获取策略测试</title>
    <link rel="stylesheet" href="public/css/custom.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            font-size: 14px;
        }
        
        .strategy-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
        }
        
        .strategy-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .strategy-icon {
            font-size: 24px;
            margin-right: 12px;
            width: 40px;
            text-align: center;
        }
        
        .strategy-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .strategy-status {
            margin-left: auto;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-waiting { background: #e3f2fd; color: #1976d2; }
        .status-running { background: #fff3e0; color: #f57c00; }
        .status-success { background: #e8f5e8; color: #2e7d32; }
        .status-failed { background: #ffebee; color: #c62828; }
        .status-skipped { background: #f3e5f5; color: #7b1fa2; }
        
        .strategy-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .strategy-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            min-height: 40px;
            display: flex;
            align-items: center;
        }
        
        .test-controls {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: background 0.3s;
        }
        
        .test-btn:hover {
            background: #45a049;
        }
        
        .test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .current-location {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .location-display {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .location-method {
            font-size: 14px;
            opacity: 0.9;
        }
        
        #banners {
            width: 100%;
            height: 200px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .strategy-card { padding: 15px; }
            .test-controls { padding: 15px; }
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h2>🌍 多层级位置获取策略测试</h2>
        <p>测试无感获取、智能推断、手动选择等多种位置获取方式</p>
        <button id="start-test-btn" class="test-btn">开始测试</button>
        <button id="reset-test-btn" class="test-btn">重置测试</button>
        <button id="manual-select-btn" class="test-btn">手动选择位置</button>
    </div>
    
    <div class="current-location">
        <div class="location-display" id="current-location">获取中...</div>
        <div class="location-method" id="location-method">等待位置获取</div>
    </div>
    
    <!-- 第一层：无感IP定位 -->
    <div class="strategy-card">
        <div class="strategy-header">
            <div class="strategy-icon">🌐</div>
            <div class="strategy-title">第一层：无感IP定位</div>
            <div class="strategy-status status-waiting" id="ip-status">等待中</div>
        </div>
        <div class="strategy-description">
            通过IP地址获取大概位置，无需用户授权，兼容性最好。使用多个IP定位服务确保成功率。
        </div>
        <div class="strategy-result" id="ip-result">等待测试开始...</div>
    </div>
    
    <!-- 第二层：GPS定位 -->
    <div class="strategy-card">
        <div class="strategy-header">
            <div class="strategy-icon">📱</div>
            <div class="strategy-title">第二层：GPS定位</div>
            <div class="strategy-status status-waiting" id="gps-status">等待中</div>
        </div>
        <div class="strategy-description">
            使用浏览器地理位置API获取精确位置。会检测隐私管家等软件的拦截情况。
        </div>
        <div class="strategy-result" id="gps-result">等待测试开始...</div>
    </div>
    
    <!-- 第三层：智能推断 -->
    <div class="strategy-card">
        <div class="strategy-header">
            <div class="strategy-icon">🧠</div>
            <div class="strategy-title">第三层：智能推断</div>
            <div class="strategy-status status-waiting" id="infer-status">等待中</div>
        </div>
        <div class="strategy-description">
            基于时区、语言、用户代理等信息推断大概位置。适用于隐私保护严格的环境。
        </div>
        <div class="strategy-result" id="infer-result">等待测试开始...</div>
    </div>
    
    <!-- 第四层：手动选择 -->
    <div class="strategy-card">
        <div class="strategy-header">
            <div class="strategy-icon">👆</div>
            <div class="strategy-title">第四层：手动选择</div>
            <div class="strategy-status status-waiting" id="manual-status">等待中</div>
        </div>
        <div class="strategy-description">
            当所有自动方式都失败时，提供友好的手动选择界面。支持搜索和热门城市快选。
        </div>
        <div class="strategy-result" id="manual-result">等待测试开始...</div>
    </div>
    
    <!-- 横幅时钟演示 -->
    <div id="banners">
        <h3 style="color: white; text-align: center; padding-top: 60px; margin: 0;">横幅时钟演示</h3>
    </div>
    
    <!-- 手动位置选择弹窗 -->
    <div id="location-selector-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; backdrop-filter: blur(5px);">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 25px; max-width: 400px; width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; color: #333;">📍 选择您的位置</h3>
                <p style="margin: 0; color: #666; font-size: 14px;">为了提供准确的天气信息，请选择您的城市</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <input type="text" id="city-search-input" placeholder="搜索城市名称..." 
                       style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px; box-sizing: border-box; outline: none; transition: border-color 0.3s;">
                <div id="city-suggestions" style="max-height: 200px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; margin-top: 10px; display: none;"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">🔥 热门城市</h4>
                <div id="popular-cities" style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <button class="city-btn" data-city="北京市">北京</button>
                    <button class="city-btn" data-city="上海市">上海</button>
                    <button class="city-btn" data-city="广州市">广州</button>
                    <button class="city-btn" data-city="深圳市">深圳</button>
                    <button class="city-btn" data-city="杭州市">杭州</button>
                    <button class="city-btn" data-city="南京市">南京</button>
                    <button class="city-btn" data-city="成都市">成都</button>
                    <button class="city-btn" data-city="武汉市">武汉</button>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="location-cancel-btn" style="padding: 10px 20px; border: 2px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer; font-size: 14px;">稍后设置</button>
                <button id="location-confirm-btn" style="padding: 10px 20px; border: none; background: #4CAF50; color: white; border-radius: 6px; cursor: pointer; font-size: 14px;" disabled>确认选择</button>
            </div>
        </div>
    </div>
    
    <style>
        .city-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            color: #333;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        .city-btn:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #2196f3;
        }
        .city-btn.selected {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }
        .city-suggestion {
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s;
        }
        .city-suggestion:hover {
            background: #f5f5f5;
        }
        .city-suggestion:last-child {
            border-bottom: none;
        }
        #city-search-input:focus {
            border-color: #4CAF50;
        }
    </style>
    
    <script src="public/js/weather-manager.js"></script>
    <script src="public/js/banner-clock.js"></script>
    <script>
        let testInProgress = false;
        
        // 更新状态显示
        function updateStatus(layer, status, result = '') {
            const statusEl = document.getElementById(`${layer}-status`);
            const resultEl = document.getElementById(`${layer}-result`);
            
            if (statusEl) {
                statusEl.className = `strategy-status status-${status}`;
                statusEl.textContent = {
                    'waiting': '等待中',
                    'running': '进行中',
                    'success': '成功',
                    'failed': '失败',
                    'skipped': '跳过'
                }[status];
            }
            
            if (resultEl && result) {
                resultEl.textContent = result;
            }
        }
        
        // 更新当前位置显示
        function updateCurrentLocation(city, method) {
            document.getElementById('current-location').textContent = city || '未获取';
            document.getElementById('location-method').textContent = method ? `获取方式：${method}` : '等待位置获取';
        }
        
        // 模拟测试各层级策略
        async function runLocationTest() {
            if (testInProgress) return;
            testInProgress = true;
            
            document.getElementById('start-test-btn').disabled = true;
            
            // 重置所有状态
            ['ip', 'gps', 'infer', 'manual'].forEach(layer => {
                updateStatus(layer, 'waiting', '等待测试开始...');
            });
            
            updateCurrentLocation('测试中...', '');
            
            try {
                // 第一层：IP定位测试
                updateStatus('ip', 'running', '正在尝试IP定位...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (window.weatherManager) {
                    const ipResult = await window.weatherManager.tryIPLocation();
                    if (ipResult) {
                        updateStatus('ip', 'success', `IP定位成功：${ipResult}`);
                        updateCurrentLocation(ipResult, 'IP定位');
                        testInProgress = false;
                        document.getElementById('start-test-btn').disabled = false;
                        return;
                    } else {
                        updateStatus('ip', 'failed', 'IP定位失败，尝试下一层级');
                    }
                } else {
                    updateStatus('ip', 'failed', '天气管理器未初始化');
                }
                
                // 第二层：GPS定位测试
                updateStatus('gps', 'running', '正在尝试GPS定位...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (window.weatherManager) {
                    const gpsResult = await window.weatherManager.tryGPSLocation();
                    if (gpsResult) {
                        updateStatus('gps', 'success', `GPS定位成功：${gpsResult}`);
                        updateCurrentLocation(gpsResult, 'GPS定位');
                        testInProgress = false;
                        document.getElementById('start-test-btn').disabled = false;
                        return;
                    } else {
                        updateStatus('gps', 'failed', 'GPS定位失败，尝试下一层级');
                    }
                } else {
                    updateStatus('gps', 'failed', '天气管理器未初始化');
                }
                
                // 第三层：智能推断测试
                updateStatus('infer', 'running', '正在进行智能推断...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (window.weatherManager) {
                    const inferResult = await window.weatherManager.tryInferLocation();
                    if (inferResult) {
                        updateStatus('infer', 'success', `智能推断成功：${inferResult}`);
                        updateCurrentLocation(inferResult, '智能推断');
                        testInProgress = false;
                        document.getElementById('start-test-btn').disabled = false;
                        return;
                    } else {
                        updateStatus('infer', 'failed', '智能推断失败，启动手动选择');
                    }
                } else {
                    updateStatus('infer', 'failed', '天气管理器未初始化');
                }
                
                // 第四层：手动选择
                updateStatus('manual', 'running', '等待用户手动选择位置...');
                showLocationSelector();
                
            } catch (error) {
                console.error('测试过程出错:', error);
                updateCurrentLocation('测试失败', '错误');
            }
            
            testInProgress = false;
            document.getElementById('start-test-btn').disabled = false;
        }
        
        // 显示位置选择器
        function showLocationSelector() {
            const modal = document.getElementById('location-selector-modal');
            if (modal) {
                modal.style.display = 'block';
                
                // 设置事件监听器
                setupLocationSelectorEvents();
            }
        }
        
        // 设置位置选择器事件
        function setupLocationSelectorEvents() {
            const searchInput = document.getElementById('city-search-input');
            const confirmBtn = document.getElementById('location-confirm-btn');
            const cancelBtn = document.getElementById('location-cancel-btn');
            const cityBtns = document.querySelectorAll('.city-btn');
            
            let selectedCity = '';
            
            // 热门城市选择
            cityBtns.forEach(btn => {
                btn.onclick = () => {
                    cityBtns.forEach(b => b.classList.remove('selected'));
                    btn.classList.add('selected');
                    selectedCity = btn.dataset.city;
                    searchInput.value = btn.textContent;
                    confirmBtn.disabled = false;
                };
            });
            
            // 确认选择
            confirmBtn.onclick = () => {
                if (selectedCity) {
                    updateStatus('manual', 'success', `手动选择成功：${selectedCity}`);
                    updateCurrentLocation(selectedCity, '手动选择');
                    document.getElementById('location-selector-modal').style.display = 'none';
                }
            };
            
            // 取消选择
            cancelBtn.onclick = () => {
                updateStatus('manual', 'skipped', '用户取消选择');
                updateCurrentLocation('北京市', '默认城市');
                document.getElementById('location-selector-modal').style.display = 'none';
            };
        }
        
        // 重置测试
        function resetTest() {
            ['ip', 'gps', 'infer', 'manual'].forEach(layer => {
                updateStatus(layer, 'waiting', '等待测试开始...');
            });
            updateCurrentLocation('等待测试', '');
            testInProgress = false;
            document.getElementById('start-test-btn').disabled = false;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌍 多层级位置获取策略测试页面加载完成');
            
            // 绑定按钮事件
            document.getElementById('start-test-btn').onclick = runLocationTest;
            document.getElementById('reset-test-btn').onclick = resetTest;
            document.getElementById('manual-select-btn').onclick = showLocationSelector;
            
            // 创建横幅时钟
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>获取天气中...</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>--</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>获取位置中...</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
            }
            
            // 初始化横幅时钟
            setTimeout(() => {
                if (typeof BannerClockWidget !== 'undefined') {
                    new BannerClockWidget();
                }
            }, 1000);
        });
    </script>
</body>
</html>
