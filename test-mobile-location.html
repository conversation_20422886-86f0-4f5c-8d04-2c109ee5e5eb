<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端位置获取优化测试</title>
    <link rel="stylesheet" href="public/css/custom.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            font-size: 14px;
        }
        
        #banners {
            width: 100%;
            height: 300px;
            margin: 10px 0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .test-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .optimization-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .debug-console {
            background: #1e1e1e;
            color: #00ff00;
            padding: 10px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .status-panel {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-value {
            font-weight: bold;
        }
        
        .status-success { color: #4caf50; }
        .status-warning { color: #ff9800; }
        .status-error { color: #f44336; }
        .status-info { color: #2196f3; }
        
        @media (max-width: 768px) {
            body { padding: 5px; }
            .test-info, .optimization-info, .status-panel { padding: 10px; }
            #banners { height: 250px; }
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>📱 移动端位置获取优化测试</h2>
        <p><strong>优化内容：</strong></p>
        <ul>
            <li>✅ 增加位置获取重试机制（最多3次）</li>
            <li>✅ 移动端超时时间延长至20秒</li>
            <li>✅ 多个地理编码服务备选</li>
            <li>✅ 延迟初始化避免页面加载冲突</li>
            <li>✅ 后台异步位置更新</li>
        </ul>
    </div>
    
    <div class="optimization-info">
        <h3>🔧 移动端优化策略</h3>
        <p><strong>问题：</strong>移动端网络环境复杂，页面加载时间长，位置获取容易超时失败</p>
        <p><strong>解决：</strong>使用渐进式加载策略，先显示缓存数据，后台异步获取真实位置</p>
    </div>
    
    <!-- 横幅时钟演示 -->
    <div id="banners">
        <h3 style="color: white; text-align: center; padding-top: 30px; margin: 0;">横幅时钟</h3>
        <!-- 横幅时钟将通过JavaScript动态添加到这里 -->
    </div>
    
    <!-- 状态面板 -->
    <div class="status-panel">
        <h3>📊 实时状态监控</h3>
        <div class="status-item">
            <span>设备类型：</span>
            <span class="status-value" id="device-type">检测中...</span>
        </div>
        <div class="status-item">
            <span>位置获取状态：</span>
            <span class="status-value" id="location-status">等待中...</span>
        </div>
        <div class="status-item">
            <span>当前城市：</span>
            <span class="status-value" id="current-city">获取中...</span>
        </div>
        <div class="status-item">
            <span>天气数据：</span>
            <span class="status-value" id="weather-status">等待中...</span>
        </div>
        <div class="status-item">
            <span>重试次数：</span>
            <span class="status-value" id="retry-count">0</span>
        </div>
        <div class="status-item">
            <span>总耗时：</span>
            <span class="status-value" id="total-time">计算中...</span>
        </div>
    </div>
    
    <!-- 调试控制台 -->
    <div class="debug-console" id="debug-console">
        <div>📱 移动端位置获取调试控制台</div>
        <div>等待初始化...</div>
    </div>
    
    <script src="public/js/weather-manager.js"></script>
    <script src="public/js/banner-clock.js"></script>
    <script>
        // 记录开始时间
        const startTime = Date.now();
        let retryCount = 0;
        
        // 调试控制台
        const debugConsole = document.getElementById('debug-console');
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;
        
        function addToDebugConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffd93d' : '#00ff00';
            div.textContent = `[${timestamp}] ${message}`;
            debugConsole.appendChild(div);
            debugConsole.scrollTop = debugConsole.scrollHeight;
            
            // 限制控制台行数
            if (debugConsole.children.length > 50) {
                debugConsole.removeChild(debugConsole.firstChild);
            }
        }
        
        // 重写console方法
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToDebugConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToDebugConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToDebugConsole(args.join(' '), 'error');
        };
        
        // 更新状态显示
        function updateStatus(elementId, value, statusClass = 'status-info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
                element.className = `status-value ${statusClass}`;
            }
        }
        
        // 检测设备类型
        function detectDevice() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const deviceType = isMobile ? '移动设备' : '桌面设备';
            updateStatus('device-type', deviceType, isMobile ? 'status-warning' : 'status-info');
            return isMobile;
        }
        
        // 监控天气管理器状态
        function monitorWeatherManager() {
            if (window.weatherManager) {
                const data = window.weatherManager.getCurrentData();
                
                // 更新城市显示
                if (data.city) {
                    updateStatus('current-city', data.city, 'status-success');
                }
                
                // 更新天气状态
                if (data.weather) {
                    updateStatus('weather-status', `${data.weather.weather} ${data.weather.temperature}°C`, 'status-success');
                } else {
                    updateStatus('weather-status', '获取中...', 'status-warning');
                }
                
                // 更新总耗时
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                updateStatus('total-time', `${elapsed}秒`, 'status-info');
            }
        }
        
        // 创建横幅时钟
        function createBannerClock() {
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>获取天气中...</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>--</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>获取位置中...</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
                console.log('✅ 横幅时钟DOM已创建');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 移动端测试页面加载完成');
            
            // 检测设备类型
            const isMobile = detectDevice();
            
            // 创建时钟DOM
            createBannerClock();
            
            // 更新位置状态
            updateStatus('location-status', '准备获取...', 'status-warning');
            
            // 定期监控状态
            setInterval(monitorWeatherManager, 2000);
            
            // 延迟初始化时钟组件
            const initDelay = isMobile ? 2000 : 1000;
            setTimeout(() => {
                console.log('🕐 开始初始化时钟组件...');
                updateStatus('location-status', '正在获取...', 'status-warning');
                
                // 初始化横幅时钟
                if (typeof BannerClockWidget !== 'undefined') {
                    try {
                        new BannerClockWidget();
                        updateStatus('location-status', '组件已启动', 'status-success');
                    } catch (error) {
                        console.error('❌ BannerClockWidget 初始化失败:', error);
                        updateStatus('location-status', '初始化失败', 'status-error');
                    }
                } else {
                    console.error('❌ BannerClockWidget 未定义');
                    updateStatus('location-status', '组件未找到', 'status-error');
                }
            }, initDelay);
        });
    </script>
</body>
</html>
