// 横幅时钟组件
class BannerClockWidget {
  constructor() {
    this.city = null;
    this.weatherData = null;
    this.weatherSubscription = null;
    this.init();
  }

  // 订阅天气数据更新
  subscribeToWeatherUpdates() {
    if (window.weatherManager) {
      this.weatherSubscription = (data) => {
        console.log('🌤️ 横幅时钟收到天气数据更新:', data);
        this.city = data.city;
        this.weatherData = data.weather;
        this.updateCityDisplay();
        this.updateWeatherDisplay();
      };

      window.weatherManager.subscribe(this.weatherSubscription);
      console.log('📡 横幅时钟已订阅天气数据');
    } else {
      console.warn('⚠️ 天气管理器未找到，横幅时钟将使用默认数据');
      this.setDefaultData();
    }
  }

  // 取消订阅
  unsubscribeFromWeatherUpdates() {
    if (window.weatherManager && this.weatherSubscription) {
      window.weatherManager.unsubscribe(this.weatherSubscription);
      console.log('📡 横幅时钟已取消订阅天气数据');
    }
  }

  // 设置默认数据
  setDefaultData() {
    this.city = '北京市';
    this.weatherData = {
      weather: '多云',
      temperature: '25',
      humidity: '65'
    };
    this.updateCityDisplay();
    this.updateWeatherDisplay();
  }

  // 初始化
  async init() {
    console.log('🕐 横幅时钟组件初始化...');

    // 启动时钟更新
    this.updateTime();
    this.startClock();

    // 等待天气管理器初始化
    if (window.weatherManager) {
      await window.weatherManager.init();
      this.subscribeToWeatherUpdates();
    } else {
      // 如果天气管理器不可用，等待一下再试
      setTimeout(() => {
        if (window.weatherManager) {
          window.weatherManager.init().then(() => {
            this.subscribeToWeatherUpdates();
          });
        } else {
          this.setDefaultData();
        }
      }, 1000);
    }
  }

  // 更新城市显示
  updateCityDisplay() {
    const cityElement = document.querySelector('#banner-clock .banner-clock-location span');
    if (cityElement && this.city) {
      cityElement.textContent = this.city;
      console.log('🏙️ 横幅时钟城市显示已更新:', this.city);
    }
  }



  // 更新天气显示
  updateWeatherDisplay() {
    if (!this.weatherData) {
      console.log('⚠️ 横幅时钟无天气数据，跳过更新');
      return;
    }

    const weatherItems = document.querySelectorAll('#banner-clock .banner-clock-weather-item span');
    if (weatherItems.length >= 2) {
      // 更新温度
      weatherItems[0].textContent = `${this.weatherData.weather} ${this.weatherData.temperature}°C`;
      // 更新湿度
      weatherItems[1].textContent = `${this.weatherData.humidity}%`;
      console.log('🌤️ 横幅时钟天气显示已更新');
    }

    // 根据天气更新图标
    this.updateWeatherIcon();
  }

  // 更新天气图标
  updateWeatherIcon() {
    if (!this.weatherData) return;

    const weatherIcon = document.querySelector('#banner-clock .banner-clock-weather-item i');
    if (!weatherIcon) return;

    const weather = this.weatherData.weather;
    let iconClass = 'fas fa-cloud-sun'; // 默认图标

    if (weather.includes('晴')) {
      iconClass = 'fas fa-sun';
    } else if (weather.includes('雨')) {
      iconClass = 'fas fa-cloud-rain';
    } else if (weather.includes('雪')) {
      iconClass = 'fas fa-snowflake';
    } else if (weather.includes('阴')) {
      iconClass = 'fas fa-cloud';
    } else if (weather.includes('雾') || weather.includes('霾')) {
      iconClass = 'fas fa-smog';
    }

    weatherIcon.className = iconClass;
  }

  // 更新时间
  updateTime() {
    const now = new Date();

    // 更新时间显示 - 手动格式化以确保正确的格式
    const timeElement = document.querySelector('#banner-clock .banner-clock-time');
    if (timeElement) {
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      timeElement.textContent = `${hours}:${minutes}:${seconds}`;
    }

    // 更新日期显示
    const dateElement = document.querySelector('#banner-clock .banner-clock-date');
    if (dateElement) {
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      const weekday = weekdays[now.getDay()];
      dateElement.textContent = `${month}月${day}日${weekday}`;
    }
  }

  // 启动时钟
  startClock() {
    // 每秒更新时间
    setInterval(() => {
      this.updateTime();
    }, 1000);
    
    console.log('⏰ 横幅时钟已启动');
  }
}

// 确保在全局作用域中可用
window.BannerClockWidget = BannerClockWidget;