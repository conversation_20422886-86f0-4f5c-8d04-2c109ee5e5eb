# Hexo 配置文件
## 文档: https://hexo.io/docs/configuration.html
## 源码: https://github.com/hexojs/hexo/

# 网站信息
title: Hexo-博客                    # 网站标题
subtitle: ''                   # 网站副标题
description: ''                # 网站描述
keywords:                      # 网站关键词
author: 千修               # 作者名称
language: zh-CN                # 网站语言，改为中文
timezone: ''                   # 时区

# URL 配置
## 在这里设置你的网站URL，例如使用GitHub Page时设置为 'https://username.github.io/project'
url: http://example.com        # 网站URL
permalink: :year/:month/:day/:title/  # 文章永久链接格式
permalink_defaults:            # 永久链接默认值
pretty_urls:
  trailing_index: true         # 设为false可移除链接末尾的'index.html'
  trailing_html: true          # 设为false可移除链接末尾的'.html'

# 目录配置
source_dir: source             # 源文件目录
public_dir: public             # 生成文件目录
tag_dir: tags                  # 标签目录
archive_dir: archives          # 归档目录
category_dir: categories       # 分类目录
code_dir: downloads/code       # 代码目录
i18n_dir: :lang               # 国际化目录
skip_render:                   # 跳过渲染的文件

# 写作配置
new_post_name: :title.md       # 新文章的文件名格式
default_layout: post           # 默认布局
titlecase: false               # 是否将标题转换为标题格式
external_link:
  enable: true                 # 在新标签页中打开外部链接
  field: site                  # 应用到整个网站
  exclude: ''
filename_case: 0               # 文件名大小写转换
render_drafts: false           # 是否渲染草稿
post_asset_folder: false       # 是否启用文章资源文件夹
relative_link: false           # 是否使用相对链接
future: true                   # 是否显示未来的文章
syntax_highlighter: highlight.js  # 语法高亮器
highlight:
  line_number: true
  auto_detect: false
  tab_replace: ''
  wrap: true
  hljs: false
prismjs:
  preprocess: true
  line_number: true
  tab_replace: ''

# 首页设置
# path: 博客首页的根路径 (默认为 '')
# per_page: 每页显示的文章数 (0 = 禁用分页)
# order_by: 文章排序 (默认按日期降序)
index_generator:
  path: ''                     # 首页路径
  per_page: 10                 # 每页文章数
  order_by: -date              # 排序方式

# 分类和标签
default_category: uncategorized  # 默认分类
category_map:                    # 分类映射
tag_map:                         # 标签映射

# Metadata elements
## https://developer.mozilla.org/en-US/docs/Web/HTML/Element/meta
meta_generator: true

# 日期/时间格式
## Hexo使用Moment.js来解析和显示日期
## 你可以自定义日期格式，参考:
## http://momentjs.com/docs/#/displaying/format/
date_format: YYYY-MM-DD        # 日期格式
time_format: HH:mm:ss          # 时间格式
## updated_option 支持 'mtime', 'date', 'empty'
updated_option: 'mtime'        # 更新选项

# 分页设置
## 设置per_page为0可禁用分页
per_page: 10                   # 每页文章数
pagination_dir: page           # 分页目录

# 包含/排除文件
## include/exclude选项仅适用于'source/'文件夹
include:                       # 包含的文件
exclude:                       # 排除的文件
ignore:                        # 忽略的文件

# 扩展
## 插件: https://hexo.io/plugins/
## 主题: https://hexo.io/themes/
theme: solitude                # 使用的主题

# 部署设置
## 文档: https://hexo.io/docs/one-command-deployment
deploy:
  type: ''                     # 部署类型

# 搜索数据库生成配置
search:
  path: search.xml
  field: post
  content: true
  format: html
