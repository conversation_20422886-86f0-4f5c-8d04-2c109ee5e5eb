<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气API优化测试</title>
    <link rel="stylesheet" href="public/css/custom.css">
    <link rel="stylesheet" href="public/css/clock-widget.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        #banners {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .optimization-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .api-stats {
            background: #fff3cd;
            border: 1px solid #ffc107;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .sidebar-demo {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-console {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🌤️ 天气API优化测试</h1>
        <p>这个页面用于测试天气API请求优化的效果。</p>
        <p><strong>优化前：</strong>横幅时钟和侧边栏时钟各自独立请求高德API</p>
        <p><strong>优化后：</strong>使用统一的天气管理器，两个组件共享一个API请求</p>
    </div>
    
    <div class="optimization-info">
        <h3>✅ 优化效果</h3>
        <ul>
            <li><strong>API调用次数减少50%</strong>：从每小时4次减少到2次</li>
            <li><strong>数据一致性</strong>：两个时钟显示相同的天气数据</li>
            <li><strong>缓存机制</strong>：避免重复请求，提高响应速度</li>
            <li><strong>统一管理</strong>：位置获取和天气更新集中处理</li>
        </ul>
    </div>
    
    <div class="api-stats">
        <h3>📊 天气API调用策略（针对5,000次/月限制优化）</h3>
        <div style="margin-bottom: 15px; padding: 10px; background: #fff3cd; border-radius: 5px;">
            <strong>🚨 重要：</strong>天气预报服务限制 <strong>5,000次/月</strong> | 安全限制：<strong>4,500次/月</strong> | 每日限制：<strong>150次</strong>
        </div>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f8f9fa;">
                <th style="padding: 10px; border: 1px solid #ddd;">时间段</th>
                <th style="padding: 10px; border: 1px solid #ddd;">基础间隔</th>
                <th style="padding: 10px; border: 1px solid #ddd;">每小时调用</th>
                <th style="padding: 10px; border: 1px solid #ddd;">动态调整</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">活跃时段<br>(9:00-18:00)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">30分钟</td>
                <td style="padding: 10px; border: 1px solid #ddd;">2次</td>
                <td style="padding: 10px; border: 1px solid #ddd;">使用率>80%时延长至60分钟</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">一般时段<br>(7:00-9:00, 18:00-22:00)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">60分钟</td>
                <td style="padding: 10px; border: 1px solid #ddd;">1次</td>
                <td style="padding: 10px; border: 1px solid #ddd;">使用率>80%时延长至120分钟</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">休息时段<br>(22:00-7:00)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">120分钟</td>
                <td style="padding: 10px; border: 1px solid #ddd;">0.5次</td>
                <td style="padding: 10px; border: 1px solid #ddd;">使用率>80%时延长至240分钟</td>
            </tr>
            <tr style="background: #e8f5e8;">
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>每日总计</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">-</td>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>约32次</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>月度约960次</strong>（远低于5,000次）</td>
            </tr>
        </table>
        <div style="margin-top: 15px; padding: 10px; background: #d4edda; border-radius: 5px;">
            <strong>实时API使用情况：</strong><span id="api-usage">加载中...</span>
        </div>
        <div style="margin-top: 10px; padding: 10px; background: #d1ecf1; border-radius: 5px;">
            <strong>💡 智能保护：</strong>系统会根据当日使用率自动调整更新频率，确保不超出限制
        </div>
    </div>
    
    <!-- 横幅时钟演示 -->
    <div id="banners">
        <h2 style="color: white; text-align: center; padding-top: 50px; margin: 0;">横幅时钟演示</h2>
        <!-- 横幅时钟将通过JavaScript动态添加到这里 -->
    </div>
    
    <!-- 侧边栏时钟演示 -->
    <div class="sidebar-demo">
        <h3>侧边栏时钟演示</h3>
        <div class="aside-content">
            <!-- 侧边栏时钟将通过JavaScript动态添加到这里 -->
        </div>
    </div>
    
    <!-- 调试控制台 -->
    <div class="debug-console" id="debug-console">
        <div>🌤️ 天气管理器调试控制台</div>
        <div>等待初始化...</div>
    </div>
    
    <script src="public/js/weather-manager.js"></script>
    <script src="public/js/banner-clock.js"></script>
    <script src="public/js/clock-widget.js"></script>
    <script>
        // 调试控制台
        const debugConsole = document.getElementById('debug-console');
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;
        
        function addToDebugConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffd93d' : '#00ff00';
            div.textContent = `[${timestamp}] ${message}`;
            debugConsole.appendChild(div);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        // 重写console方法以显示在页面上
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToDebugConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToDebugConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToDebugConsole(args.join(' '), 'error');
        };
        
        // 创建横幅时钟
        function createBannerClock() {
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>多云 25°C</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>65%</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>获取位置中...</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
                console.log('✅ 横幅时钟DOM已创建');
            }
        }
        
        // 创建侧边栏时钟
        function createSidebarClock() {
            const asideContent = document.querySelector('.aside-content');
            if (asideContent && !document.querySelector('.clock-widget')) {
                const clockWidget = document.createElement('div');
                clockWidget.className = 'card-widget card-clock';
                clockWidget.innerHTML = `
                    <div class="card-content">
                        <div class="clock-widget">
                            <div class="clock-header">
                                <span class="clock-date"></span>
                                <span class="clock-weather">
                                    <i class="fas fa-cloud-sun"></i>
                                    <span>多云 25°C</span>
                                    <i class="fas fa-tint"></i>
                                    <span>65%</span>
                                </span>
                            </div>
                            <div class="clock-time">加载中...</div>
                            <div class="clock-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="clock-city">获取中...</span>
                            </div>
                        </div>
                    </div>
                `;
                asideContent.appendChild(clockWidget);
                console.log('✅ 侧边栏时钟DOM已创建');
            }
        }
        
        // 更新API使用情况显示
        function updateApiUsage() {
            if (window.weatherManager) {
                const data = window.weatherManager.getCurrentData();
                const apiUsageElement = document.getElementById('api-usage');
                if (apiUsageElement && data.apiStats) {
                    const stats = data.apiStats;
                    const dailyStatus = stats.dailyPercentage > 80 ? '🚨 接近限制' :
                                       stats.dailyPercentage > 50 ? '⚡ 正常使用' : '✅ 使用良好';
                    const monthlyStatus = stats.monthlyPercentage > 90 ? '🚨 严重警告' :
                                         stats.monthlyPercentage > 80 ? '⚠️ 接近限制' :
                                         stats.monthlyPercentage > 50 ? '⚡ 正常使用' : '✅ 使用良好';

                    apiUsageElement.innerHTML = `
                        <div style="margin-bottom: 8px;">
                            <strong>今日：</strong>${stats.todayCount} / ${stats.dailyLimit} 次
                            (<strong>${stats.dailyPercentage}%</strong>)
                            <span style="color: ${stats.dailyPercentage > 80 ? 'red' : stats.dailyPercentage > 50 ? 'orange' : 'green'};">
                                ${dailyStatus}
                            </span>
                        </div>
                        <div>
                            <strong>本月：</strong>${stats.monthlyCount} / ${stats.monthlyLimit} 次
                            (<strong>${stats.monthlyPercentage}%</strong>)
                            <span style="color: ${stats.monthlyPercentage > 90 ? 'red' : stats.monthlyPercentage > 80 ? 'orange' : 'green'};">
                                ${monthlyStatus}
                            </span>
                        </div>
                    `;
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 创建时钟DOM
            createBannerClock();
            createSidebarClock();

            // 初始化时钟组件
            setTimeout(() => {
                console.log('🕐 开始初始化时钟组件...');

                // 初始化横幅时钟
                if (typeof BannerClockWidget !== 'undefined') {
                    new BannerClockWidget();
                } else {
                    console.error('❌ BannerClockWidget 未定义');
                }

                // 初始化侧边栏时钟
                if (typeof ClockWidget !== 'undefined') {
                    new ClockWidget();
                } else {
                    console.error('❌ ClockWidget 未定义');
                }

                // 定期更新API使用情况显示
                setInterval(updateApiUsage, 5000); // 每5秒更新一次
                setTimeout(updateApiUsage, 2000); // 2秒后首次更新
            }, 500);
        });
    </script>
</body>
</html>
