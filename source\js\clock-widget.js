// 时钟组件
class ClockWidget {
  constructor() {
    this.city = null;
    this.weatherData = null;
    this.weatherSubscription = null;
    this.autoThemeStorageKey = 'auto-theme-enabled';
    this.init();
  }

  // 订阅天气数据更新
  subscribeToWeatherUpdates() {
    if (window.weatherManager) {
      this.weatherSubscription = (data) => {
        console.log('🌤️ 侧边栏时钟收到天气数据更新:', data);
        this.city = data.city;
        this.weatherData = data.weather;
        this.updateCityDisplay();
        this.updateWeatherDisplay();
      };

      window.weatherManager.subscribe(this.weatherSubscription);
      console.log('📡 侧边栏时钟已订阅天气数据');
    } else {
      console.warn('⚠️ 天气管理器未找到，侧边栏时钟将使用默认数据');
      this.setDefaultData();
    }
  }

  // 取消订阅
  unsubscribeFromWeatherUpdates() {
    if (window.weatherManager && this.weatherSubscription) {
      window.weatherManager.unsubscribe(this.weatherSubscription);
      console.log('📡 侧边栏时钟已取消订阅天气数据');
    }
  }

  // 设置默认数据
  setDefaultData() {
    this.city = '北京市';
    this.weatherData = {
      weather: '多云',
      temperature: '25',
      humidity: '65'
    };
    this.updateCityDisplay();
    this.updateWeatherDisplay();
  }

  // 更新城市显示
  updateCityDisplay() {
    const cityElement = document.querySelector('.clock-city');
    if (cityElement && this.city) {
      cityElement.textContent = this.city;
      console.log('🏙️ 侧边栏时钟城市显示已更新:', this.city);
    }
  }

  // 检查是否启用自动主题切换
  isAutoThemeEnabled() {
    try {
      const setting = localStorage.getItem(this.autoThemeStorageKey);
      return setting !== 'false'; // 默认启用，除非明确设置为false
    } catch (error) {
      return true; // 默认启用
    }
  }

  // 设置自动主题切换
  setAutoTheme(enabled) {
    try {
      localStorage.setItem(this.autoThemeStorageKey, enabled.toString());
      console.log('🎨 自动主题切换设置:', enabled ? '启用' : '禁用');
    } catch (error) {
      console.warn('⚠️ 保存自动主题设置失败:', error);
    }
  }

  // 自动主题切换逻辑
  autoSwitchTheme() {
    if (!this.isAutoThemeEnabled()) {
      return;
    }

    const now = new Date();
    const hour = now.getHours();
    
    // 避免频繁检查，只在小时变化时检查
    if (this.lastThemeCheck === hour) {
      return;
    }
    
    this.lastThemeCheck = hour;
    
    // 获取当前主题状态
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark' || 
                      document.body.classList.contains('dark-mode') ||
                      document.body.getAttribute('data-theme') === 'dark';
    
    // 定义主题切换时间：6:00-18:00为浅色主题，18:00-6:00为深色主题
    const shouldBeDark = hour < 6 || hour >= 18;
    
    // 如果当前主题与应该的主题不匹配，则切换
    if (shouldBeDark && !isDarkMode) {
      console.log('🌙 自动切换到深色主题 (当前时间:', hour + ':00)');
      this.switchToDarkMode();
    } else if (!shouldBeDark && isDarkMode) {
      console.log('☀️ 自动切换到浅色主题 (当前时间:', hour + ':00)');
      this.switchToLightMode();
    }
  }

  // 切换到深色主题
  switchToDarkMode() {
    try {
      // 尝试调用现有的主题切换函数
      if (typeof sco !== 'undefined' && sco.switchDarkMode) {
        // 检查当前是否为浅色主题，如果是则切换
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark' || 
                      document.body.classList.contains('dark-mode');
        if (!isDark) {
          sco.switchDarkMode();
        }
      } else {
        // 备用方案：直接设置主题属性
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-mode');
        localStorage.setItem('theme', 'dark');
      }
    } catch (error) {
      console.warn('⚠️ 切换到深色主题失败:', error);
    }
  }

  // 切换到浅色主题
  switchToLightMode() {
    try {
      // 尝试调用现有的主题切换函数
      if (typeof sco !== 'undefined' && sco.switchDarkMode) {
        // 检查当前是否为深色主题，如果是则切换
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark' || 
                      document.body.classList.contains('dark-mode');
        if (isDark) {
          sco.switchDarkMode();
        }
      } else {
        // 备用方案：直接设置主题属性
        document.documentElement.setAttribute('data-theme', 'light');
        document.body.classList.remove('dark-mode');
        localStorage.setItem('theme', 'light');
      }
    } catch (error) {
      console.warn('⚠️ 切换到浅色主题失败:', error);
    }
  }



  async init() {
    this.createClockElement();
    this.updateTime();
    this.setupEventListeners();

    // 每秒更新时间
    setInterval(() => this.updateTime(), 1000);

    // 等待天气管理器初始化
    if (window.weatherManager) {
      await window.weatherManager.init();
      this.subscribeToWeatherUpdates();
    } else {
      // 如果天气管理器不可用，等待一下再试
      setTimeout(() => {
        if (window.weatherManager) {
          window.weatherManager.init().then(() => {
            this.subscribeToWeatherUpdates();
          });
        } else {
          this.setDefaultData();
        }
      }, 1000);
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    // 延迟设置事件监听器，确保DOM元素已创建
    setTimeout(() => {
      const autoThemeSwitch = document.getElementById('auto-theme-switch');
      if (autoThemeSwitch) {
        autoThemeSwitch.addEventListener('change', (e) => {
          const enabled = e.target.checked;
          this.setAutoTheme(enabled);
          
          if (enabled) {
            console.log('✅ 自动主题切换已启用');
            // 立即检查一次主题
            this.lastThemeCheck = null;
            this.autoSwitchTheme();
          } else {
            console.log('❌ 自动主题切换已禁用');
          }
        });
      }
    }, 100);
  }

  createClockElement() {
    const clockHTML = `
      <div class="clock-widget">
        <div class="clock-header">
          <span class="clock-date"></span>
          <span class="clock-weather">
            <i class="fas fa-cloud-sun"></i>
            <span>多云 37°C</span>
            <i class="fas fa-tint"></i>
            <span>39%</span>
          </span>
        </div>
        <div class="clock-time">16:46:15</div>
        <div class="clock-location">
          <i class="fas fa-map-marker-alt"></i>
          <span class="clock-city">获取中...</span>
        </div>
        <div class="clock-controls">
          <div class="auto-theme-toggle">
            <i class="fas fa-palette"></i>
            <span>自动主题</span>
            <label class="switch">
              <input type="checkbox" id="auto-theme-switch" ${this.isAutoThemeEnabled() ? 'checked' : ''}>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>
    `;
    

  }

  updateTime() {
    const now = new Date();
    const timeElement = document.querySelector('.clock-time');
    const dateElement = document.querySelector('.clock-date');
    
    if (timeElement && dateElement) {
      // 格式化时间
      const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      
      // 格式化日期
      const dateString = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'short'
      });
      
      timeElement.textContent = timeString;
      dateElement.textContent = dateString;
    }
    
    // 检查并执行自动主题切换
    this.autoSwitchTheme();
  }







  updateWeatherDisplay(error = false) {
    const weatherElement = document.querySelector('.clock-weather');

    if (weatherElement) {
      if (error || !this.weatherData) {
        weatherElement.innerHTML = `
          <i class="fas fa-cloud"></i>
          <span>多云 25°C</span>
          <i class="fas fa-tint"></i>
          <span>65%</span>
        `;
        console.log('⚠️ 侧边栏时钟使用默认天气显示');
      } else {
        const weather = this.weatherData;
        const weatherIcon = this.getWeatherIcon(weather.weather);

        weatherElement.innerHTML = `
          <i class="${weatherIcon}"></i>
          <span>${weather.weather} ${weather.temperature}°C</span>
          <i class="fas fa-tint"></i>
          <span>${weather.humidity}%</span>
        `;
        console.log('🌤️ 侧边栏时钟天气显示已更新');
      }
    }
  }

  getWeatherIcon(weather) {
    const iconMap = {
      '晴': 'fas fa-sun',
      '多云': 'fas fa-cloud-sun',
      '阴': 'fas fa-cloud',
      '雨': 'fas fa-cloud-rain',
      '雪': 'fas fa-snowflake',
      '雾': 'fas fa-smog',
      '霾': 'fas fa-smog'
    };
    
    for (const key in iconMap) {
      if (weather.includes(key)) {
        return iconMap[key];
      }
    }
    
    return 'fas fa-cloud';
  }
}

// 页面加载完成后初始化时钟组件
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    new ClockWidget();
  }, 1000);
});

if (typeof pjax !== 'undefined') {
  document.addEventListener('pjax:complete', () => {
    setTimeout(() => {
      new ClockWidget();
    }, 1000);
  });
}