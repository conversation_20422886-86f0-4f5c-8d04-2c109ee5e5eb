<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置点击选择测试</title>
    <link rel="stylesheet" href="public/css/custom.css">
    <link rel="stylesheet" href="public/css/clock-widget.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .demo-item {
            text-align: center;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        #banners {
            width: 100%;
            height: 300px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-bottom: 20px;
        }
        
        .sidebar-demo {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1976d2;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .feature-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 6px;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            body {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🖱️ 位置点击选择功能测试</h1>
            <p>点击时钟组件的位置信息来手动选择城市</p>
        </div>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p><strong>操作方式：</strong>将鼠标悬停在位置信息上会显示"点击更改"提示，点击即可打开位置选择器</p>
            <p><strong>支持组件：</strong>横幅时钟（左下角位置）和侧边栏时钟（底部位置）</p>
        </div>
        
        <div class="demo-grid">
            <!-- 横幅时钟演示 -->
            <div class="demo-item">
                <div class="demo-title">横幅时钟演示</div>
                <div id="banners">
                    <div style="color: white; text-align: center; padding-top: 50px;">
                        <h3 style="margin: 0;">横幅时钟</h3>
                        <p style="margin: 10px 0; opacity: 0.8;">悬停位置信息查看提示</p>
                        <p style="margin: 0; font-size: 14px; opacity: 0.7;">👆 点击左下角位置信息</p>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏时钟演示 -->
            <div class="demo-item">
                <div class="demo-title">侧边栏时钟演示</div>
                <div class="sidebar-demo">
                    <div class="aside-content">
                        <div style="text-align: center; color: #666; margin-bottom: 20px;">
                            <p>👆 点击下方位置信息</p>
                        </div>
                        <!-- 侧边栏时钟将在这里创建 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>✨ 功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">🖱️</div>
                    <div>
                        <strong>悬停提示：</strong>鼠标悬停在位置信息上时显示"点击更改"提示
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div>
                        <strong>点击触发：</strong>点击位置信息即可打开手动位置选择器
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div>
                        <strong>移动端优化：</strong>支持触摸操作，自动弹出键盘便于搜索
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔍</div>
                    <div>
                        <strong>智能搜索：</strong>支持城市名称搜索和热门城市快选
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💾</div>
                    <div>
                        <strong>记忆功能：</strong>选择的城市会被保存，下次访问时自动使用
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🌤️</div>
                    <div>
                        <strong>即时更新：</strong>选择新城市后立即获取对应的天气信息
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎨 视觉效果</h3>
            <ul>
                <li><strong>悬停效果：</strong>位置信息在悬停时会有高亮和轻微上移效果</li>
                <li><strong>提示气泡：</strong>悬停时显示"点击更改"或"点击更改位置"提示</li>
                <li><strong>光标变化：</strong>鼠标悬停时光标变为手型，表示可点击</li>
                <li><strong>深色模式：</strong>自动适配深色主题的颜色方案</li>
            </ul>
        </div>
    </div>
    
    <!-- 手动位置选择弹窗 -->
    <div id="location-selector-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; backdrop-filter: blur(5px);">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 25px; max-width: 400px; width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; color: #333;">📍 选择您的位置</h3>
                <p style="margin: 0; color: #666; font-size: 14px;">为了提供准确的天气信息，请选择您的城市</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <input type="text" id="city-search-input" placeholder="搜索城市名称..." 
                       style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px; box-sizing: border-box; outline: none; transition: border-color 0.3s;">
                <div id="city-suggestions" style="max-height: 200px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; margin-top: 10px; display: none;"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">🔥 热门城市</h4>
                <div id="popular-cities" style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <button class="city-btn" data-city="北京市">北京</button>
                    <button class="city-btn" data-city="上海市">上海</button>
                    <button class="city-btn" data-city="广州市">广州</button>
                    <button class="city-btn" data-city="深圳市">深圳</button>
                    <button class="city-btn" data-city="杭州市">杭州</button>
                    <button class="city-btn" data-city="南京市">南京</button>
                    <button class="city-btn" data-city="成都市">成都</button>
                    <button class="city-btn" data-city="武汉市">武汉</button>
                </div>
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="location-cancel-btn" style="padding: 10px 20px; border: 2px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer; font-size: 14px;">取消</button>
                <button id="location-confirm-btn" style="padding: 10px 20px; border: none; background: #4CAF50; color: white; border-radius: 6px; cursor: pointer; font-size: 14px;" disabled>确认选择</button>
            </div>
        </div>
    </div>
    
    <style>
        .city-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            color: #333;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        .city-btn:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #2196f3;
        }
        .city-btn.selected {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }
        .city-suggestion {
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s;
        }
        .city-suggestion:hover {
            background: #f5f5f5;
        }
        .city-suggestion:last-child {
            border-bottom: none;
        }
        #city-search-input:focus {
            border-color: #4CAF50;
        }
    </style>
    
    <script src="public/js/weather-manager.js"></script>
    <script src="public/js/banner-clock.js"></script>
    <script src="public/js/clock-widget.js"></script>
    <script>
        // 创建横幅时钟
        function createBannerClock() {
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>获取天气中...</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>--</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>获取位置中...</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
                console.log('✅ 横幅时钟DOM已创建');
            }
        }
        
        // 创建侧边栏时钟
        function createSidebarClock() {
            const asideContent = document.querySelector('.aside-content');
            if (asideContent && !document.querySelector('.clock-widget')) {
                const clockWidget = document.createElement('div');
                clockWidget.className = 'card-widget card-clock';
                clockWidget.innerHTML = `
                    <div class="card-content">
                        <div class="clock-widget">
                            <div class="clock-header">
                                <span class="clock-date"></span>
                                <span class="clock-weather">
                                    <i class="fas fa-cloud-sun"></i>
                                    <span>获取中...</span>
                                    <i class="fas fa-tint"></i>
                                    <span>--</span>
                                </span>
                            </div>
                            <div class="clock-time">加载中...</div>
                            <div class="clock-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="clock-city">获取中...</span>
                            </div>
                        </div>
                    </div>
                `;
                asideContent.appendChild(clockWidget);
                console.log('✅ 侧边栏时钟DOM已创建');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🖱️ 位置点击选择测试页面加载完成');
            
            // 创建时钟DOM
            createBannerClock();
            createSidebarClock();
            
            // 延迟初始化时钟组件
            setTimeout(() => {
                console.log('🕐 开始初始化时钟组件...');
                
                // 初始化横幅时钟
                if (typeof BannerClockWidget !== 'undefined') {
                    try {
                        new BannerClockWidget();
                        console.log('✅ 横幅时钟组件初始化成功');
                    } catch (error) {
                        console.error('❌ 横幅时钟组件初始化失败:', error);
                    }
                }
                
                // 初始化侧边栏时钟
                if (typeof ClockWidget !== 'undefined') {
                    try {
                        new ClockWidget();
                        console.log('✅ 侧边栏时钟组件初始化成功');
                    } catch (error) {
                        console.error('❌ 侧边栏时钟组件初始化失败:', error);
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
