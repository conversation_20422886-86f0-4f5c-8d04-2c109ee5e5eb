---
title: 参数保存
cover: https://s21.ax1x.com/2025/07/15/pV1JTyV.jpg
keywords: []
date: 2025-07-15 13:53:57
tags: [3XUI, Cloudflare, ArgoSB, 隧道, 代理]
categories: [技术教程]
description: 保存重要的服务器配置参数，包括3XUI面板地址、CF固定隧道密钥和ArgoSB脚本命令
---

<style>
.terminal-block {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    font-family: 'Courier New', monospace;
}

.terminal-header {
    background: linear-gradient(90deg, #3a3a3a 0%, #4a4a4a 100%);
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #555;
}

.terminal-title {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f57; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #28ca42; }

.terminal-content {
    padding: 15px;
    color: #00ff00;
    font-size: 14px;
    position: relative;
    background: #1e1e1e;
}

.terminal-prompt {
    color: #00ff00;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-btn:hover {
    background: #005a9e;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.textContent;
        button.textContent = '已复制';
        button.style.background = '#28a745';
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '#007acc';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
    });
}
</script>

## 1. 3XUI面板地址

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">3XUI面板地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://aws.0407123.xyz:56789/qianxiu/
        <button class="copy-btn" onclick="copyTerminalContent('https://aws.0407123.xyz:56789/qianxiu/', this)">复制</button>
    </div>
</div>

## 2. CF固定隧道密钥

### 网址：psy.0407123.xyz

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">CF隧道密钥 - psy.0407123.xyz</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>eyJhIjoiY2YxNzgzMzU3ODliNjA0ZTc5N2NjMmEwMTZiZGU4YzEiLCJ0IjoiNGFiMWY3MmYtYTRmNS00MDIyLThiYjUtMjc2NThlMzZjYzZkIiwicyI6Ik1tUTFaalF3TjJFdE5EYzFZeTAwWmpVeExXRTJNMkV0TmpkbFptWmpNVFZqT1RoaiJ9
        <button class="copy-btn" onclick="copyTerminalContent('eyJhIjoiY2YxNzgzMzU3ODliNjA0ZTc5N2NjMmEwMTZiZGU4YzEiLCJ0IjoiNGFiMWY3MmYtYTRmNS00MDIyLThiYjUtMjc2NThlMzZjYzZkIiwicyI6Ik1tUTFaalF3TjJFdE5EYzFZeTAwWmpVeExXRTJNMkV0TmpkbFptWmpNVFZqT1RoaiJ9', this)">复制</button>
    </div>
</div>

## 3. ArgoSB一键无交互代理脚本

### 脚本命令

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">ArgoSB脚本</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>vmpt="" argo="y" agn="" agk="" bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh)
        <button class="copy-btn" onclick="copyTerminalContent('vmpt=&quot;&quot; argo=&quot;y&quot; agn=&quot;&quot; agk=&quot;&quot; bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh)', this)">复制</button>
    </div>
</div>

### AGSB快捷方式

#### 1. 查看Argo信息

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">查看Argo信息</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>agsb list
        <button class="copy-btn" onclick="copyTerminalContent('agsb list', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">查看Argo信息（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list
        <button class="copy-btn" onclick="copyTerminalContent('bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list', this)">复制</button>
    </div>
</div>

#### 2. 在线切换IPV4/IPV6节点配置（双栈VPS专享）

**显示IPV4节点配置：**

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV4节点配置</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=4 agsb list
        <button class="copy-btn" onclick="copyTerminalContent('ip=4 agsb list', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV4节点配置（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=4 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list
        <button class="copy-btn" onclick="copyTerminalContent('ip=4 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list', this)">复制</button>
    </div>
</div>

**显示IPV6节点配置：**

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV6节点配置</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=6 agsb list
        <button class="copy-btn" onclick="copyTerminalContent('ip=6 agsb list', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">显示IPV6节点配置（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>ip=6 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list
        <button class="copy-btn" onclick="copyTerminalContent('ip=6 bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) list', this)">复制</button>
    </div>
</div>

#### 3. 卸载脚本

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">卸载脚本</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>agsb del
        <button class="copy-btn" onclick="copyTerminalContent('agsb del', this)">复制</button>
    </div>
</div>

或者：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">卸载脚本（完整命令）</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) del
        <button class="copy-btn" onclick="copyTerminalContent('bash <(curl -Ls https://raw.githubusercontent.com/yonggekkk/argosb/main/argosb.sh) del', this)">复制</button>
    </div>
</div>

---

## ⚠️ 免责声明

本文档仅供学习和研究使用，请勿用于任何非法活动。使用者需遵守当地法律法规，作者不承担任何责任。请在24小时内删除相关内容。