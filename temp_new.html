<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1"><title>Hexo-博客</title><noscript>开启JavaScript才能访问本站哦~</noscript><link rel="icon" href="/img/photo.jpg"><!-- index.css--><link rel="stylesheet" href="/css/index.css?v=3.0.19"><!-- inject head--><link rel="canonical" href="http://example.com/index.html"><link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/all.min.css"><!-- aplayer--><!-- swiper--><!-- fancybox ui--><!-- katex--><!-- Open Graph--><meta name="description"><!-- pwa--><meta name="apple-mobile-web-app-capable" content="Hexo-博客"><meta name="theme-color" content="var(--efu-main)"><meta name="apple-mobile-web-app-status-bar-style" content="var(--efu-main)"><link rel="bookmark" href="/img/photo.jpg"><link rel="apple-touch-icon" href="/img/photo.jpg" sizes="180x180"><script>console.log(' %c Solitude %c ' + '3.0.19' + ' %c https://github.com/everfu/hexo-theme-solitude',
    'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
    'background:#ff9a9a ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
    'background:unset ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff')
</script><link rel="stylesheet" href="/css/custom.css"><link rel="stylesheet" href="/css/clock-widget.css"><script src="/js/clock-widget.js" defer></script><script>(()=>{
        const saveToLocal = {
            set: function setWithExpiry(key, value, ttl) {
                if (ttl === 0)
                    return
                const now = new Date()
                const expiryDay = ttl * 86400000
                const item = {
                    value: value,
                    expiry: now.getTime() + expiryDay
                }
                localStorage.setItem(key, JSON.stringify(item))
            },
            get: function getWithExpiry(key) {
                const itemStr = localStorage.getItem(key)

                if (!itemStr) {
                    return undefined
                }
                const item = JSON.parse(itemStr)
                const now = new Date()

                if (now.getTime() > item.expiry) {
                    localStorage.removeItem(key)
                    return undefined
                }
                return item.value
            }
        };
        window.utils = {
            saveToLocal: saveToLocal,
            getCSS: (url, id = false) => new Promise((resolve, reject) => {
              const link = document.createElement('link')
              link.rel = 'stylesheet'
              link.href = url
              if (id) link.id = id
              link.onerror = reject
              link.onload = link.onreadystatechange = function() {
                const loadState = this.readyState
                if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
                link.onload = link.onreadystatechange = null
                resolve()
              }
              document.head.appendChild(link)
            }),
            getScript: (url, attr = {}) => new Promise((resolve, reject) => {
              const script = document.createElement('script')
              script.src = url
              script.async = true
              script.onerror = reject
              script.onload = script.onreadystatechange = function() {
                const loadState = this.readyState
                if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
                script.onload = script.onreadystatechange = null
                resolve()
              }

              Object.keys(attr).forEach(key => {
                script.setAttribute(key, attr[key])
              })

              document.head.appendChild(script)
            }),
            addGlobalFn: (key, fn, name = false, parent = window) => {
                const globalFn = parent.globalFn || {}
                const keyObj = globalFn[key] || {}

                if (name && keyObj[name]) return

                name = name || Object.keys(keyObj).length
                keyObj[name] = fn
                globalFn[key] = keyObj
                parent.globalFn = globalFn
            },
            addEventListenerPjax: (ele, event, fn, option = false) => {
              ele.addEventListener(event, fn, option)
              utils.addGlobalFn('pjax', () => {
                  ele.removeEventListener(event, fn, option)
              })
            },
            diffDateFormat: (selector) => {
                selector.forEach(item => {
                    const date = new Date(item.getAttribute('datetime') || item.textContent);
                    item.textContent = (date.getMonth() + 1).toString()+'/'+date.getDate().toString();
                });
            },
        }
    })()</script><!-- theme--><script>initTheme = () => {
    let isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
    const cachedMode = utils.saveToLocal.get('theme');
    if (cachedMode === undefined) {
        const nowMode =
            isDarkMode ? 'dark' : 'light'
        document.documentElement.setAttribute('data-theme', nowMode);
    } else {
        document.documentElement.setAttribute('data-theme', cachedMode);
    }
    typeof rm === 'object' && rm.mode(cachedMode === 'dark' && isDarkMode)
}
initTheme()</script><!-- global head--><script>const GLOBAL_CONFIG = {
    root: '/',
    algolia: undefined,
    localsearch: {"preload":false,"path":"/search.xml"},
    runtime: '2023-04-20 00:00:00',
    lazyload: {
        enable: false,
        error: '/img/error_load.avif'
    },
    copyright: false,
    highlight: {"limit":200,"expand":true,"copy":true,"syntax":"highlight.js"},
    randomlink: false,
    lang: {"theme":{"dark":"已切换至深色模式","light":"已切换至浅色模式"},"copy":{"success":"复制成功","error":"复制失败"},"backtop":"返回顶部","time":{"day":"天前","hour":"小时前","just":"刚刚","min":"分钟前","month":"个月前"},"day":" 天","f12":"开发者模式已打开，请遵循GPL协议。","totalk":"无需删除空行，直接输入评论即可","search":{"empty":"找不到你查询的内容：${query}","hit":"找到 ${hits} 条结果，用时 ${time} 毫秒","placeholder":"输入关键词快速查找","count":"共 <b>${count}</b> 条结果。","loading":"搜索中..."}},
    aside: {
        state: {
            morning: "✨ 早上好，新的一天开始了",
            noon: "🍲 午餐时间",
            afternoon: "🌞 下午好",
            night: "早点休息",
            goodnight: "晚安 😴",
        },
        witty_words: [],
        witty_comment: {
            prefix: '好久不见，',
            back: '欢迎再次回来，',
        },
    },
    covercolor: {
        enable: false
    },
    comment: false,
    lightbox: 'null',
    right_menu: false,
    translate: {"translateDelay":0,"defaultEncoding":2},
    lure: false,
    expire: false,
};</script><!-- page-config head--><script id="config-diff">var PAGE_CONFIG = {
    is_post: false,
    is_page: false,
    is_home: true,
    page: 'solitude',
    toc: false,
    comment: false,
    ai_text: false,
    color: false,
}</script><meta name="generator" content="Hexo 7.3.0"></head><body id="body" data-type="solitude"><!-- universe--><!-- background img--><!-- loading--><!-- console--><!-- sidebar--><div id="sidebar" style="zoom: 1;"><div id="menu-mask" style="display: none;"></div><div id="sidebar-menus"><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">12</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">8</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">33</div></a></div></div></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><span class="darkmode_switchbutton menu-child" onclick="sco.switchDarkMode()"><i class="solitude fas fa-circle-half-stroke"></i><span>显示模式</span></span></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><span>首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span>文库</span></a><ul class="menus_item_child"><li><a class="site-page child" href="/archives/"><i class="solitude  fas fa-folder-closed"></i><span>全部文章</span></a></li><li><a class="site-page child" href="/categories/"><i class="solitude  fas fa-clone"></i><span>全部分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="solitude  fas fa-tags"></i><span>全部标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span>友链</span></a><ul class="menus_item_child"><li><a class="site-page child" target="_blank" rel="noopener" href="https://github.com/qianxiu203"><i class="solitude  fas fa-user-group"></i><span>友情链接</span></a></li><li><a class="site-page child" target="_blank" rel="noopener" href="https://t.me/qianxiua"><i class="solitude  fas fa-wifi"></i><span>朋友圈</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child" target="_blank" rel="noopener" href="https://github.com/qianxiu203"><i class="solitude  fas fa-user"></i><span>关于我</span></a></li><li><a class="site-page child" href="/tools/"><i class="solitude  fas fa-toolbox"></i><span>在线工具</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="card-tag-cloud"><a href="/tags/AI/">AI<sup>3</sup></a><a href="/tags/%E8%81%8A%E5%A4%A9%E5%BA%94%E7%94%A8/">聊天应用<sup>1</sup></a><a href="/tags/%E5%BC%80%E6%BA%90/">开源<sup>2</sup></a><a href="/tags/LLM/">LLM<sup>2</sup></a><a href="/tags/%E6%90%9C%E7%B4%A2%E5%BC%95%E6%93%8E/">搜索引擎<sup>1</sup></a><a href="/tags/Perplexity/">Perplexity<sup>1</sup></a><a href="/tags/Emby/">Emby<sup>1</sup></a><a href="/tags/%E5%BD%B1%E8%A7%86/">影视<sup>2</sup></a><a href="/tags/%E6%B5%81%E5%AA%92%E4%BD%93/">流媒体<sup>2</sup></a><a href="/tags/Git/">Git<sup>1</sup></a><a href="/tags/GitHub/">GitHub<sup>1</sup></a><a href="/tags/%E5%A4%87%E4%BB%BD/">备份<sup>1</sup></a><a href="/tags/%E6%95%99%E7%A8%8B/">教程<sup>1</sup></a><a href="/tags/%E5%9C%A8%E7%BA%BF%E8%A7%82%E7%9C%8B/">在线观看<sup>1</sup></a><a href="/tags/LibreTV/">LibreTV<sup>1</sup></a><a href="/tags/%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90/">影视资源<sup>1</sup></a><a href="/tags/%E4%BB%A3%E7%90%86/">代理<sup>2</sup></a><a href="/tags/%E8%8A%82%E7%82%B9/">节点<sup>1</sup></a><a href="/tags/3XUI/">3XUI<sup>1</sup></a><a href="/tags/Cloudflare/">Cloudflare<sup>3</sup></a><a href="/tags/ArgoSB/">ArgoSB<sup>1</sup></a><a href="/tags/%E9%9A%A7%E9%81%93/">隧道<sup>1</sup></a><a href="/tags/%E6%96%B0%E9%97%BB/">新闻<sup>1</sup></a><a href="/tags/%E8%B5%84%E8%AE%AF/">资讯<sup>1</sup></a><a href="/tags/%E8%81%9A%E5%90%88/">聚合<sup>1</sup></a><a href="/tags/%E6%8F%90%E7%A4%BA%E8%AF%8D/">提示词<sup>1</sup></a><a href="/tags/%E4%BC%98%E5%8C%96%E5%B7%A5%E5%85%B7/">优化工具<sup>1</sup></a><a href="/tags/%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96/">性能优化<sup>1</sup></a><a href="/tags/%E7%9B%91%E6%8E%A7%E7%B3%BB%E7%BB%9F/">监控系统<sup>1</sup></a><a href="/tags/%E8%BF%90%E7%BB%B4%E5%B7%A5%E5%85%B7/">运维工具<sup>1</sup></a><a href="/tags/%E7%BD%91%E7%BB%9C%E4%BB%A3%E7%90%86/">网络代理<sup>1</sup></a><a href="/tags/%E8%AE%A2%E9%98%85%E8%BD%AC%E6%8D%A2/">订阅转换<sup>1</sup></a><a href="/tags/%E8%AE%A2%E9%98%85%E7%AE%A1%E7%90%86/">订阅管理<sup>1</sup></a></div></div></div></div><!-- keyboard--><!-- righhtside--><div class="page" id="body-wrap"><header class="not-top-img" id="page-header"><nav class="show" id="nav"><div id="nav-group"><div id="blog_name"><a id="site-name" href="/" title="返回博客主页"><span class="title">主页</span><i class="solitude fas fa-home"></i></a></div><div id="page-name-mask"><div id="page-name"><a id="page-name-text" onclick="sco.toTop()">Hexo-博客</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><span>首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span>文库</span></a><ul class="menus_item_child"><li><a class="site-page child" href="/archives/"><i class="solitude  fas fa-folder-closed"></i><span>全部文章</span></a></li><li><a class="site-page child" href="/categories/"><i class="solitude  fas fa-clone"></i><span>全部分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="solitude  fas fa-tags"></i><span>全部标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span>友链</span></a><ul class="menus_item_child"><li><a class="site-page child" target="_blank" rel="noopener" href="https://github.com/qianxiu203"><i class="solitude  fas fa-user-group"></i><span>友情链接</span></a></li><li><a class="site-page child" target="_blank" rel="noopener" href="https://t.me/qianxiua"><i class="solitude  fas fa-wifi"></i><span>朋友圈</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child" target="_blank" rel="noopener" href="https://github.com/qianxiu203"><i class="solitude  fas fa-user"></i><span>关于我</span></a></li><li><a class="site-page child" href="/tools/"><i class="solitude  fas fa-toolbox"></i><span>在线工具</span></a></li></ul></div></div></div><div id="nav-left"></div><div id="nav-right"><div class="nav-button" id="github_button"><a class="site-page" target="_blank" rel="noopener" href="https://github.com/qianxiu203" title=""><i class="solitude fab fa-github"></i></a></div><div class="nav-button" id="theme_switch_button"><a class="site-page" href="javascript:void(0)" onclick="sco.switchDarkMode()" title=""><i class="solitude fas fa-circle-half-stroke"></i></a></div><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机一篇文章" href="javascript:void(0);"><i class="solitude fas fa-dice-d6"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索"><i class="solitude fas fa-magnifying-glass"></i></a></div><div class="nav-button" id="nav-totop" onclick="sco.toTop()"><a class="totopbtn"><i class="solitude fas fa-arrow-up"></i><span id="percent">0</span></a></div><div id="toggle-menu"><a class="site-page"><i class="solitude fas fa-bars"></i></a></div></div></div></nav></header><div id="home_top"><div class="recent-top-post-group" id="recent-top-post-group"><div class="recent-post-top" id="recent-post-top"><div id="bannerGroup"><div id="banners"><div class="banners-title"><div class="banners-title-big">分享技术<br >与科技生活</div><div class="banners-title-small">一个热爱生活的人</div></div><div class="tags-group-all"><div class="tags-group-wrapper"><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background: #e9572b"><img class="nolazyload" src="https://i.postimg.cc/vBWVnY8q/html.png" title="HTML"></div><div class="tags-group-icon" style="background: #f7cb4f"><img class="nolazyload" src="https://i.postimg.cc/3N10Ltv2/js.png" title="JS"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background: #57b6e6"><img class="nolazyload" src="https://i.postimg.cc/8Pk6Fg24/docker.png" title="Docker"></div><div class="tags-group-icon" style="background: #ffffff"><img class="nolazyload" src="https://i.postimg.cc/hPC7T3gB/flutter.png" title="Flutter"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background: #2e3a41"><img class="nolazyload" src="https://i.postimg.cc/dVLZBmtT/webpack.png" title="WebPack"></div><div class="tags-group-icon" style="background: #df5b40"><img class="nolazyload" src="https://i.postimg.cc/nhgjwjCS/git.png" title="Git"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background: #e9572b"><img class="nolazyload" src="https://i.postimg.cc/vBWVnY8q/html.png" title="HTML"></div><div class="tags-group-icon" style="background: #f7cb4f"><img class="nolazyload" src="https://i.postimg.cc/3N10Ltv2/js.png" title="JS"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background: #57b6e6"><img class="nolazyload" src="https://i.postimg.cc/8Pk6Fg24/docker.png" title="Docker"></div><div class="tags-group-icon" style="background: #ffffff"><img class="nolazyload" src="https://i.postimg.cc/hPC7T3gB/flutter.png" title="Flutter"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background: #2e3a41"><img class="nolazyload" src="https://i.postimg.cc/dVLZBmtT/webpack.png" title="WebPack"></div><div class="tags-group-icon" style="background: #df5b40"><img class="nolazyload" src="https://i.postimg.cc/nhgjwjCS/git.png" title="Git"></div></div></div></div></div></div><div class="topGroup"><script>function GoTodayCard() {
    window.open("https://solitude.js.org/", "_blank");
}</script><div class="todayCard" id="todayCard" onclick="GoTodayCard()"><div class="todayCard-info"><div class="todayCard-tips">置顶</div><div class="todayCard-title">Solitude 官方文档</div></div><div class="todayCard-cover nolazyload" style="background: url('/img/default.avif') no-repeat center /cover"></div></div></div></div></div></div><main class="layout" id="content-inner"><div id="home"><div id="category-bar"><div class="category-bar-items home" id="category-bar-items"><div class="category-bar-item select" id="category-bar-home"><a href="/">推荐</a></div><div class="category-bar-item"><a href="/archives/">全部文章</a></div><div class="category-bar-item" id="AI应用"><a href="/categories/AI%E5%BA%94%E7%94%A8/">AI应用</a></div><div class="category-bar-item" id="影视娱乐"><a href="/categories/%E5%BD%B1%E8%A7%86%E5%A8%B1%E4%B9%90/">影视娱乐</a></div><div class="category-bar-item" id="技术教程"><a href="/categories/%E6%8A%80%E6%9C%AF%E6%95%99%E7%A8%8B/">技术教程</a></div><div class="category-bar-item" id="影视资源"><a href="/categories/%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90/">影视资源</a></div><div class="category-bar-item" id="网络工具"><a href="/categories/%E7%BD%91%E7%BB%9C%E5%B7%A5%E5%85%B7/">网络工具</a></div><div class="category-bar-item" id="信息服务"><a href="/categories/%E4%BF%A1%E6%81%AF%E6%9C%8D%E5%8A%A1/">信息服务</a></div><div class="category-bar-item" id="技术实战"><a href="/categories/%E6%8A%80%E6%9C%AF%E5%AE%9E%E6%88%98/">技术实战</a></div><div class="category-bar-item" id="工具推荐"><a href="/categories/%E5%B7%A5%E5%85%B7%E6%8E%A8%E8%8D%90/">工具推荐</a></div></div><div class="category-bar-right"><a class="category-bar-more" href="/categories/">更多</a></div></div><div class="recent-posts" id="recent-posts"><div class="recent-post-item" onclick="pjax.loadUrl('/2025/07/19/%E8%AE%A2%E9%98%85%E8%BF%BD%E8%B8%AA%E7%AE%A1%E7%90%86/')"><div class="post_cover"><a href="/2025/07/19/%E8%AE%A2%E9%98%85%E8%BF%BD%E8%B8%AA%E7%AE%A1%E7%90%86/" title="订阅追踪管理"><img class="post_bg" src="https://s21.ax1x.com/2025/07/19/pV3O6ER.jpg" alt="订阅追踪管理"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">工具推荐</span><span class="original">最新</span><a class="unvisited-post" href="/2025/07/19/%E8%AE%A2%E9%98%85%E8%BF%BD%E8%B8%AA%E7%AE%A1%E7%90%86/">未读</a></div><a class="article-title" href="/2025/07/19/%E8%AE%A2%E9%98%85%E8%BF%BD%E8%B8%AA%E7%AE%A1%E7%90%86/" title="订阅追踪管理">订阅追踪管理</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Cloudflare/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>Cloudflare</span></a><a class="article-meta__tags" href="/tags/%E8%AE%A2%E9%98%85%E7%AE%A1%E7%90%86/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>订阅管理</span></a></span><span class="post-meta-date"><time datetime="2025-07-19T10:14:55.230Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/07/15/%E5%8F%82%E6%95%B0%E4%BF%9D%E5%AD%98/')"><div class="post_cover"><a href="/2025/07/15/%E5%8F%82%E6%95%B0%E4%BF%9D%E5%AD%98/" title="参数保存"><img class="post_bg" src="https://s21.ax1x.com/2025/07/15/pV1JTyV.jpg" alt="参数保存"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">技术教程</span><a class="unvisited-post" href="/2025/07/15/%E5%8F%82%E6%95%B0%E4%BF%9D%E5%AD%98/">未读</a></div><a class="article-title" href="/2025/07/15/%E5%8F%82%E6%95%B0%E4%BF%9D%E5%AD%98/" title="参数保存">参数保存</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BB%A3%E7%90%86/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>代理</span></a><a class="article-meta__tags" href="/tags/3XUI/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>3XUI</span></a><a class="article-meta__tags" href="/tags/Cloudflare/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>Cloudflare</span></a><a class="article-meta__tags" href="/tags/ArgoSB/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>ArgoSB</span></a><a class="article-meta__tags" href="/tags/%E9%9A%A7%E9%81%93/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>隧道</span></a></span><span class="post-meta-date"><time datetime="2025-07-15T05:53:57.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/30/%E7%AB%99%E7%82%B9%E7%9B%91%E6%8E%A7%E5%B9%B3%E5%8F%B0/')"><div class="post_cover"><a href="/2025/06/30/%E7%AB%99%E7%82%B9%E7%9B%91%E6%8E%A7%E5%B9%B3%E5%8F%B0/" title="站点监控平台"><img class="post_bg" src="https://cdn4.winhlb.com/2025/06/30/68626a92dd46e.jpeg" alt="站点监控平台"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">技术实战</span><a class="unvisited-post" href="/2025/06/30/%E7%AB%99%E7%82%B9%E7%9B%91%E6%8E%A7%E5%B9%B3%E5%8F%B0/">未读</a></div><a class="article-title" href="/2025/06/30/%E7%AB%99%E7%82%B9%E7%9B%91%E6%8E%A7%E5%B9%B3%E5%8F%B0/" title="站点监控平台">站点监控平台</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>性能优化</span></a><a class="article-meta__tags" href="/tags/%E7%9B%91%E6%8E%A7%E7%B3%BB%E7%BB%9F/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>监控系统</span></a><a class="article-meta__tags" href="/tags/%E8%BF%90%E7%BB%B4%E5%B7%A5%E5%85%B7/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>运维工具</span></a></span><span class="post-meta-date"><time datetime="2025-06-29T16:00:00.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/27/%E5%85%8D%E8%B4%B9%E8%8A%82%E7%82%B9/')"><div class="post_cover"><a href="/2025/06/27/%E5%85%8D%E8%B4%B9%E8%8A%82%E7%82%B9/" title="免费内容"><img class="post_bg" src="https://cdn4.winhlb.com/2025/06/27/685d830497f1e.jpeg" alt="免费内容"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">网络工具</span><a class="unvisited-post" href="/2025/06/27/%E5%85%8D%E8%B4%B9%E8%8A%82%E7%82%B9/">未读</a></div><a class="article-title" href="/2025/06/27/%E5%85%8D%E8%B4%B9%E8%8A%82%E7%82%B9/" title="免费内容">免费内容</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BB%A3%E7%90%86/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>代理</span></a><a class="article-meta__tags" href="/tags/%E8%8A%82%E7%82%B9/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>节点</span></a></span><span class="post-meta-date"><time datetime="2025-06-26T17:28:39.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/08/emby%E5%BD%B1%E8%A7%86/')"><div class="post_cover"><a href="/2025/06/08/emby%E5%BD%B1%E8%A7%86/" title="emby影视"><img class="post_bg" src="https://cdn4.winhlb.com/2025/06/08/684545d0c695c.png" alt="emby影视"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">影视娱乐</span><a class="unvisited-post" href="/2025/06/08/emby%E5%BD%B1%E8%A7%86/">未读</a></div><a class="article-title" href="/2025/06/08/emby%E5%BD%B1%E8%A7%86/" title="emby影视">emby影视</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Emby/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>Emby</span></a><a class="article-meta__tags" href="/tags/%E5%BD%B1%E8%A7%86/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>影视</span></a><a class="article-meta__tags" href="/tags/%E6%B5%81%E5%AA%92%E4%BD%93/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>流媒体</span></a></span><span class="post-meta-date"><time datetime="2025-06-08T08:13:17.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/08/LobeChat/')"><div class="post_cover"><a href="/2025/06/08/LobeChat/" title="LobeChat - 开源AI聊天应用与开发框架完全指南"><img class="post_bg" src="https://s21.ax1x.com/2025/07/10/pVQbdiD.png" alt="LobeChat - 开源AI聊天应用与开发框架完全指南"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">AI应用</span><a class="unvisited-post" href="/2025/06/08/LobeChat/">未读</a></div><a class="article-title" href="/2025/06/08/LobeChat/" title="LobeChat - 开源AI聊天应用与开发框架完全指南">LobeChat - 开源AI聊天应用与开发框架完全指南</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/AI/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>AI</span></a><a class="article-meta__tags" href="/tags/%E8%81%8A%E5%A4%A9%E5%BA%94%E7%94%A8/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>聊天应用</span></a><a class="article-meta__tags" href="/tags/%E5%BC%80%E6%BA%90/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>开源</span></a><a class="article-meta__tags" href="/tags/LLM/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>LLM</span></a></span><span class="post-meta-date"><time datetime="2025-06-07T18:05:00.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/08/%E8%AE%A2%E9%98%85%E8%BD%AC%E6%8D%A2/')"><div class="post_cover"><a href="/2025/06/08/%E8%AE%A2%E9%98%85%E8%BD%AC%E6%8D%A2/" title="汇聚订阅 - 在Cloudflare上高效管理节点订阅"><img class="post_bg" src="https://s21.ax1x.com/2025/07/10/pVQbHe0.png" alt="汇聚订阅 - 在Cloudflare上高效管理节点订阅"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">网络工具</span><a class="unvisited-post" href="/2025/06/08/%E8%AE%A2%E9%98%85%E8%BD%AC%E6%8D%A2/">未读</a></div><a class="article-title" href="/2025/06/08/%E8%AE%A2%E9%98%85%E8%BD%AC%E6%8D%A2/" title="汇聚订阅 - 在Cloudflare上高效管理节点订阅">汇聚订阅 - 在Cloudflare上高效管理节点订阅</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Cloudflare/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>Cloudflare</span></a><a class="article-meta__tags" href="/tags/%E7%BD%91%E7%BB%9C%E4%BB%A3%E7%90%86/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>网络代理</span></a><a class="article-meta__tags" href="/tags/%E8%AE%A2%E9%98%85%E8%BD%AC%E6%8D%A2/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>订阅转换</span></a></span><span class="post-meta-date"><time datetime="2025-06-07T17:31:35.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/06/libretv-yingshi/')"><div class="post_cover"><a href="/2025/06/06/libretv-yingshi/" title="LibreTV影视 - 优质在线影视平台使用指南"><img class="post_bg" src="https://s21.ax1x.com/2025/07/10/pVQbssI.png" alt="LibreTV影视 - 优质在线影视平台使用指南"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">影视资源</span><a class="unvisited-post" href="/2025/06/06/libretv-yingshi/">未读</a></div><a class="article-title" href="/2025/06/06/libretv-yingshi/" title="LibreTV影视 - 优质在线影视平台使用指南">LibreTV影视 - 优质在线影视平台使用指南</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%BD%B1%E8%A7%86/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>影视</span></a><a class="article-meta__tags" href="/tags/%E6%B5%81%E5%AA%92%E4%BD%93/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>流媒体</span></a><a class="article-meta__tags" href="/tags/%E5%9C%A8%E7%BA%BF%E8%A7%82%E7%9C%8B/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>在线观看</span></a><a class="article-meta__tags" href="/tags/LibreTV/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>LibreTV</span></a><a class="article-meta__tags" href="/tags/%E5%BD%B1%E8%A7%86%E8%B5%84%E6%BA%90/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>影视资源</span></a></span><span class="post-meta-date"><time datetime="2025-06-05T16:00:00.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/06/05/%E6%96%B0%E9%97%BB%E5%BF%AB%E9%80%92/')"><div class="post_cover"><a href="/2025/06/05/%E6%96%B0%E9%97%BB%E5%BF%AB%E9%80%92/" title="新闻快递 - 全球实时资讯聚合平台"><img class="post_bg" src="https://s21.ax1x.com/2025/07/10/pVQbNdK.jpg" alt="新闻快递 - 全球实时资讯聚合平台"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">信息服务</span><a class="unvisited-post" href="/2025/06/05/%E6%96%B0%E9%97%BB%E5%BF%AB%E9%80%92/">未读</a></div><a class="article-title" href="/2025/06/05/%E6%96%B0%E9%97%BB%E5%BF%AB%E9%80%92/" title="新闻快递 - 全球实时资讯聚合平台">新闻快递 - 全球实时资讯聚合平台</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%96%B0%E9%97%BB/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>新闻</span></a><a class="article-meta__tags" href="/tags/%E8%B5%84%E8%AE%AF/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>资讯</span></a><a class="article-meta__tags" href="/tags/%E8%81%9A%E5%90%88/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>聚合</span></a></span><span class="post-meta-date"><time datetime="2025-06-05T15:01:42.000Z"></time></span></div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/2025/01/27/github%E6%BA%90%E7%A0%81%E5%A4%87%E4%BB%BD%E6%95%99%E7%A8%8B/')"><div class="post_cover"><a href="/2025/01/27/github%E6%BA%90%E7%A0%81%E5%A4%87%E4%BB%BD%E6%95%99%E7%A8%8B/" title="github源码备份教程"><img class="post_bg" src="https://s21.ax1x.com/2025/07/14/pV1FvRA.jpg" alt="github源码备份教程"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><span class="original">技术教程</span><a class="unvisited-post" href="/2025/01/27/github%E6%BA%90%E7%A0%81%E5%A4%87%E4%BB%BD%E6%95%99%E7%A8%8B/">未读</a></div><a class="article-title" href="/2025/01/27/github%E6%BA%90%E7%A0%81%E5%A4%87%E4%BB%BD%E6%95%99%E7%A8%8B/" title="github源码备份教程">github源码备份教程</a></div><div class="article-meta-wrap"><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Git/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>Git</span></a><a class="article-meta__tags" href="/tags/GitHub/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>GitHub</span></a><a class="article-meta__tags" href="/tags/%E5%A4%87%E4%BB%BD/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>备份</span></a><a class="article-meta__tags" href="/tags/%E6%95%99%E7%A8%8B/" onclick="event.stopPropagation();"><span class="tags-punctuation"><i class="solitude fas fa-hashtag"></i>教程</span></a></span><span class="post-meta-date"><time datetime="2025-01-27T02:00:00.000Z"></time></span></div></div></div><!-- pagination--><nav id="pagination"><div class="pagination"><span class="page-number current">1</span><a class="page-number" href="/page/2/">2</a><a class="extend next" rel="next" href="/page/2/"><div class="pagination_tips_next">下一页</div> <i class="solitude fas fa-chevron-right"></i></a><div class="toPageGroup"><input id="toPageText" oninput="value=value.replace(/[^0-9]/g,'')" maxlength="3" title="跳转到指定页面" onkeyup="if (this.value === '0') this.value = ''"><a id="toPageButton" onclick="sco.toPage()"><i class="solitude fas fa-angles-right"></i></a></div></div></nav></div></div><!-- aside--><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="card-content"><div class="card-info-avatar is-center"><div class="top-group"><div class="sayhi" id="sayhi" onclick="sco.changeWittyWord()"></div></div></div><div class="avatar"><img alt="头像" src="/img/photo.jpg"></div><div class="description"></div><div class="bottom-group"><span class="left"><div class="name">千修</div><div class="desc">只有迎风，风筝才能飞得更高。</div></span><div class="social-icons is-center"></div></div></div></div><div class="sticky_layout"><div class="card-widget card-tags card-archives card-webinfo card-allinfo"><div class="webinfo"><div class="webinfo-item"><div class="item-name">文章总数 :</div><div class="item-count">12</div></div><div class="webinfo-item"><div class="item-name">建站天数 :</div><div class="item-count" id="runtimeshow"></div></div><div class="webinfo-item"><div class="item-name">最后更新 :</div><time class="item-count" datetime="2025-07-19T14:33:05.364Z"></time></div></div></div></div></div></main><footer id="footer"><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div class="copyright">© 2023 - 2025 By&nbsp;<a class="footer-bar-link" href="/"><img class="author-avatar" src="/img/photo.jpg">千修</a></div><div class="beian-group"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://hexo.io/">框架：Hexo</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://github.com/everfu/hexo-theme-solitude">主题：Solitude</a></div></div></div></div></footer></div><!-- right_menu--><!-- inject body--><div><script src="/js/utils.js?v=3.0.19"></script><script src="/js/main.js?v=3.0.19"></script><script src="/js/third_party/waterfall.min.js?v=3.0.19"></script><script src="https://fastly.jsdelivr.net/npm/pjax@0.2.8/pjax.min.js"></script><script src="/js/tw_cn.js?v=3.0.19"></script><script src="https://fastly.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>window.paceOptions = {
  restartOnPushState: false
}

utils.addGlobalFn('pjaxSend', () => {
  Pace.restart()
}, 'pace_restart')
</script><script src="https://fastly.jsdelivr.net/npm/pace-js@1.2.4/pace.min.js"></script><script>
document.addEventListener('DOMContentLoaded', function() {
  // 创建横幅时钟
  function createBannerClock() {
    const banners = document.getElementById('banners');
    if (banners && !document.getElementById('banner-clock')) {
      const bannerClock = document.createElement('div');
      bannerClock.id = 'banner-clock';
      bannerClock.innerHTML = `
        <div class="banner-clock-time"></div>
        <div class="banner-clock-date"></div>
        <div class="banner-clock-location">
          <i class="fas fa-map-marker-alt"></i>
          <span>北京, 中国</span>
        </div>
      `;
      banners.appendChild(bannerClock);

      // 更新横幅时钟
      function updateBannerClock() {
        const timeEl = bannerClock.querySelector('.banner-clock-time');
        const dateEl = bannerClock.querySelector('.banner-clock-date');
        if (timeEl && dateEl) {
          const now = new Date();
          timeEl.textContent = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
          });
          dateEl.textContent = now.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric',
            weekday: 'short'
          });
        }
      }
      updateBannerClock();
      setInterval(updateBannerClock, 1000);
    }
  }

  // 等待侧边栏加载完成
  setTimeout(function() {
    const asideContent = document.querySelector('.aside-content');
    const cardInfo = document.querySelector('.card-widget.card-info');
    if (asideContent && cardInfo) {
      // 创建时钟组件
      const clockWidget = document.createElement('div');
      clockWidget.className = 'card-widget card-clock';
      clockWidget.id = 'aside-clock';
      clockWidget.innerHTML = `
        <div class="card-content">
          <div class="clock-widget">
            <div class="clock-header">
              <span class="clock-date"></span>
              <span class="clock-weather">
                <i class="fas fa-cloud-sun"></i>
                <span>多云 37°C</span>
                <i class="fas fa-tint"></i>
                <span>39%</span>
              </span>
            </div>
            <div class="clock-time">16:46:15</div>
            <div class="clock-location">
              <i class="fas fa-map-marker-alt"></i>
              <span class="clock-city">获取中...</span>
            </div>
          </div>
        </div>
      `;
      // 在信息卡后面插入时钟组件
      cardInfo.parentNode.insertBefore(clockWidget, cardInfo.nextSibling);
      
      // 初始化时钟功能
      if (typeof ClockWidget !== 'undefined') {
        new ClockWidget();
      }
    }

    // 创建横幅时钟
    createBannerClock();
  }, 500);
});

// PJAX支持
if (typeof pjax !== 'undefined') {
  document.addEventListener('pjax:complete', function() {
    setTimeout(function() {
      const asideContent = document.querySelector('.aside-content');
      const cardInfo = document.querySelector('.card-widget.card-info');
      const existingClock = document.querySelector('#aside-clock');
      
      if (asideContent && cardInfo && !existingClock) {
        const clockWidget = document.createElement('div');
        clockWidget.className = 'card-widget card-clock';
        clockWidget.id = 'aside-clock';
        clockWidget.innerHTML = `
          <div class="card-content">
            <div class="clock-widget">
              <div class="clock-header">
                <span class="clock-date"></span>
                <span class="clock-weather">
                  <i class="fas fa-cloud-sun"></i>
                  <span>多云 37°C</span>
                  <i class="fas fa-tint"></i>
                  <span>39%</span>
                </span>
              </div>
              <div class="clock-time">16:46:15</div>
              <div class="clock-location">
                <i class="fas fa-map-marker-alt"></i>
                <span class="clock-city">获取中...</span>
              </div>
            </div>
          </div>
        `;
        cardInfo.parentNode.insertBefore(clockWidget, cardInfo.nextSibling);
        
        if (typeof ClockWidget !== 'undefined') {
          new ClockWidget();
        }
      }

      // 重新创建横幅时钟
      createBannerClock();
    }, 500);
  });
}
</script>
<div class="js-pjax"></div></div><!-- pjax--><script>const pjax = new Pjax({
    elements: 'a:not([target="_blank"])',
    selectors: ["title","#body-wrap","#site-config","meta[name=\"description\"]",".js-pjax","meta[property^=\"og:\"]","#config-diff",".rs_show",".rs_hide"],
    cacheBust: false,
    analytics: false,
    scrollRestoration: false
})

document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
})

document.addEventListener('pjax:complete', () => {
    window.refreshFn()

    document.querySelectorAll('script[data-pjax]').forEach(item => {
        const newScript = document.createElement('script')
        const content = item.text || item.textContent || item.innerHTML || ""
        Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
        newScript.appendChild(document.createTextNode(content))
        item.parentNode.replaceChild(newScript, item)
    })

    GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

})

document.addEventListener('pjax:error', (e) => {
    if (e.request.status === 404) {
        pjax.loadUrl('/404.html')
    }
})</script><!-- google adsense--><!-- search--><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="solitude fas fa-xmark"></i></button></nav><div class="search-wrap"><div class="search-box"><input class="search-box-input" id="search-input" type="text" autocomplete="off" spellcheck="false" autocorrect="off" autocapitalize="off" placeholder="输入关键词快速查找"></div><div id="search-results"><div id="search-hits"></div></div><div id="search-pagination"></div><div id="search-tips"></div></div></div><div id="search-mask"></div></div><script src="/js/search/local.js?v=3.0.19"></script><!-- music--></body></html>
        <script>
            const posts = ["2025/06/08/LobeChat/","2025/01/15/Perplexica/","2025/06/08/emby影视/","2025/01/27/github源码备份教程/","2025/06/06/libretv-yingshi/","2025/06/27/免费节点/","2025/07/15/参数保存/","2025/06/05/新闻快递/","2025/01/15/提示词优化器/","2025/06/30/站点监控平台/","2025/06/08/订阅转换/","2025/07/19/订阅追踪管理/"];
            function toRandomPost() {
                const randomPost = posts[Math.floor(Math.random() * posts.length)];
                pjax.loadUrl(GLOBAL_CONFIG.root + randomPost);
            }
        </script>